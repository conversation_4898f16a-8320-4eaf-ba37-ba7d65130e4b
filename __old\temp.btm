
IF /i EXIST I:\__backup\snapshot\tree\c.txt DEL /y I:\__backup\snapshot\tree\c.txt
START /c /low /min /inv "tree c" TREE /bh c:\ > I:\__backup\snapshot\tree\c.txt

GOSUB indexTree d
GOSUB indexTree e
GOSUB indexTree i
GOSUB indexTree j
GOSUB indexTree m

GOTO 

:indexTree [drive]
IF /i EXIST I:\__backup\snapshot\tree\%drive%.txt DEL /y I:\__backup\snapshot\tree\%drive%.txt
START /c /low /min /inv /wait "tree %drive%" TREE /bh %drive%:\ > I:\__backup\snapshot\tree\%drive%.txt
RETURN

