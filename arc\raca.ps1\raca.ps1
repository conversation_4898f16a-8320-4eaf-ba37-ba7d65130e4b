<#
.SYNOPSIS
Raca looks for old archives and recompress. 
It works on directories or single files

.DESCRIPTION
A more detailed description of your script or function.

.PARAMETER ParameterName
Description of the parameter, its type, and any restrictions.

.EXAMPLE
Example of how to use your script or function.

.NOTES
Additional information or notes about your script or function.

.LINK
Link to more information or documentation if available.
#>
# ---------------------------------------------------------------------------------------
# Define custom switch parameters and their descriptions
[CmdletBinding(DefaultParameterSetName="A")]
param (
    #,Position=0,ParameterSetName="A"
    [Parameter(Mandatory=$true)]
    [string]$SRC_PATH,


    [Parameter(Mandatory=$true)]
    [string]$TARGET_DIR, # this is the target location, ie recompress to a new target... otherwise its in place... 

    [Parameter(Mandatory=$false)]
    [string[]]$TEMP_DIRS,

    [Parameter(Mandatory=$false)]
    [switch]$RECURSIVE,

    [Parameter(Mandatory=$false)]
    [boolean]$FOLLOW=$false,

    [Parameter(Mandatory=$false)]
    [int]$WAIT
)
$ErrorActionPreference = "Stop"
$PSStyle.Progress.view_string = "Minimal"
# ---------------------------------------------------------------------------------------
class MLOG {
    [string]$PREFIX="->"
    [hashtable]$f=@{
        quiet = $False
        debug = $False
    }
    MLOG([string]$prefix,[boolean]$quiet){
        $this.PREFIX=$prefix
        $this.f.quiet=$quiet
    }
    MLOG([boolean]$quiet){
        $this.f.quiet=$quiet
    }
    setDebug([boolean]$state){$this.f.debug=$state}
    # used by each log level
    [void]_print([array]$in_colours,[array]$logs){
        if(-not $this.f.quiet) {
            if ($in_colours.Length -eq 3) {
                $used_colours=@("DarkBlue")+$in_colours
            } else {$used_colours=$in_colours}
            Write-Host $this.PREFIX -NoNewLine -ForegroundColor $used_colours[0];
            Write-Host $logs[0] -NoNewline -ForegroundColor $used_colours[1];
            Write-Host $logs[1] -NoNewline -ForegroundColor $used_colours[2];
            Write-Host $logs[2] -ForegroundColor $used_colours[3];
        }
    }
    # uses default used_colours
    [void]_print([array]$logs){
        if(-not $this.f.quiet){
            Write-Host $this.PREFIX -NoNewLine -ForegroundColor DarkGray;
            Write-Host $logs[0] -NoNewline -ForegroundColor DarkGray;
            Write-Host $logs[1] -NoNewline -ForegroundColor Gray;
            Write-Host $logs[2] -ForegroundColor White;
        }
    }
    [void]testing($log){
        Write-Host $log[0] -ForegroundColor DarkCyan;
    }
    [void]one($log){if(-not $this.f.quiet){Write-Host $log[0] -ForegroundColor Gray}}
    [void]o([System.Array]$log){$this.one($log)}

    [void]br([array]$logs){if(-not $this.f.quiet){Write-Host}}
    [void]b([array]$logs){$this.br()}
    
    # default
    [void]def([array]$logs){$this._print($logs)}
    [void]d([array]$logs){$this.def($logs)}
    
    #Black,DarkBlue,DarkGreen,DarkCyan,DarkRed,DarkMagenta,DarkYellow,Gray,DarkGray,Blue,Green,Cyan,Red,Magenta,Yellow,White

    static[array]$ERROR_COLOURS=@("DarkRed","DarkRed","Red")
    [void]error([array]$logs){$this._print([MLOG]::ERROR_COLOURS,$logs)}
    [void]err([array]$logs){$this.error($logs)}
    [void]e([array]$logs){$this.error($logs)}

    [void]errorbr([array]$logs){Write-Host;$this.error($logs);Write-Host}
    [void]eb([array]$logs){$this.errorbr($logs)}
    
    static[array]$GOOD_COLOURS=@("DarkGreen","Green","Green")
    [void]good([array]$logs){$this._print([MLOG]::GOOD_COLOURS,$logs)}
    [void]g([array]$logs){$this.good($logs)}

    static[array]$HEAD_COLOURS=@("DarkCyan","White","White")
    [void]head([array]$logs){$this._print([MLOG]::HEAD_COLOURS,$logs)}
    [void]h([array]$logs){$this.head($logs)}
    
    static[array]$INFO_COLOURS=@("DarkBlue","Blue","Cyan")
    [void]info([array]$logs){$this._print([MLOG]::INFO_COLOURS,$logs)}
    [void]i([array]$logs){$this.info($logs)}
    
    static[array]$WARNING_COLOURS=@("DarkYellow","Yellow","Magenta")
    [void]warning([array]$logs){$this._print([MLOG]::WARNING_COLOURS,$logs)}
    [void]warn([array]$logs){$this.warning($logs)}
    [void]w([array]$logs){$this.warning($logs)}
}
$script:L = [MLOG]::new($BATCH)
$L.setDebug($True)
# ---------------------------------------------------------------------------------------

class RACA {
    static $CONFIG = {
        companions = @{
            "7z.exe" = @{
            }
        }
        arc_opts = @{
            exe = @{
                dictionary_size = 256    
            }
            default = @{
                dictionary_size = 224
            }
        }
        matching_extensions = [string[]]@(
            "*.7z",
            "*.tar",
            #"*.tgz",
            "*.zip",
            #"*.bz2",
            #"*.tar.gz",
            #"*.tar.bz2",
            "*.rar"
        )
        search_options = @{
            #older_than_years = 5
            older_date = $Null 
            #[datetime]"1/1/2020"
            # controls teh archiove operations
            min_size = $Null 
            #32KB
            max_size = $Null 
            #300MB
            max_inner_files = $Null
            # limit the operations to no of files
            #max_count = 5
        }
        behaviour = @{
            # if the archive contains a pointless dir other than first level
            normalise_archives = $False
        }
    }
    $args = @{
            
    }
    $job = @{
        p = @{
            src_path = $Null
            target_dir = $Null

            log = "D:\usr\var\local\raca\logs"
            
            temp = [string[]]@(
                "X:\tmp\_tmp\raca\d\x",           
                "E:\tmp\_tmp\raca\d\e",
                "M:\tmp\_tmp\raca\d\m",
                "S:\tmp\_tmp\raca\d\s",
                "W:\tmp\_tmp\raca\d\w"
            )
        }
        op = @{
            cycle_pause = 240
        }
        c = @{
            f = @{
                is_one_file = $False
                is_one_exe = $False
                is_another_arc = $false
                is_one_dir = $False
                # ie a child dir but with nothing in it when combined with 
                is_dir_within_dir = $False
                is_child_one_dir = $False
            }
        }
        d = @{
            eligible_files = @{}
            selected_tmp_dir = $Null
            took = $Null
            completed = @{}
            failed = @{}
        }
        f = @{
            status = $Null
            recurse = $False
        }
    }
    ProcessArguments() {
        # $TARGET,
        # $TEMP_DIRS,
        # $WAIT,
        # $FOLLOW,
        # $RECURSIVE
        if ($this.args.RECURSIVE) {
            $this.job.f.recurse = $True
        }
        if ($this.args.TARGET_DIR) {
    
        }
    }        
}

# todo: avoid massive files mode or limit from easy powershell sizes
function GetCompressedFilesFromRoot (
    $options = $Null
) {
    $options = @{
        Include = $CONFIG.matching_extensions
        Path = $SRC
        Recurse = $CONFIG.search_options.recurse  
    }
    #`
    #| #Where-Object {
    #  ($_.Length -gt $CONFIG.constraints.min_size) -and
    #  ($_.Length -lt $CONFIG.constraints.max_size) -and
    #  ($_.LastWriteTime) -lt (Get-Date).Subtract(-$CONFIG.constraints.older_date)
    #})
    $SESSION.eligible_files = Get-ChildItem @options
    return $SESSION.eligible_files
}

# Get-AppFilePath 

Function CreateMissingDirectories {
    $create_paths = $CONFIG.paths.temp += $CONFIG.paths.log
    $script:L.i(@("Checking for $($create_paths.Length)", " paths..."))
    foreach($p in $create_paths) {
        if (-not (Test-Path -Path $p)) {
            $script:L.w(@("Creating missing directory:", $p))
            New-Item -Force -Type Directory -Path $p
        } else {
            $script:L.i(@("Dir exists:", $p))
        }
    }
}

#Function waitForConf { Start-Sleep -Seconds $CONFIG.op.cycle_pause }
#Function waitForX {[param]$x _waitfor $x }

# fix this into how extraca works

# TODO: took field
# TODO: progress bar
Function ProcessCompressedFileListSlowly {
    $current_tmp_index = 0 
    foreach ($src_path in $SESSION.eligible_files) {

        $script:L.i(@("Processing: ", $current_tmp_index, " of $($SESSION.d.eligible_files.Length)"))

        $SESSION.CURRENT_ARCHIVE.FI = (Get-Item $src_path)

        if ($True -eq $SESSION.CURRENT_ARCHIVE.FI.Directory) {
            $SESSION.CURRENT_ARCHIVE.status = "invalid"
            $Script:L.e(@("Not a file: ", $src_path))
            break
        }

        $script:L.i(@("Path: ", $src_path))
        $SESSION.CURRENT_ARCHIVE.src_path = $src_path

        # TODO: first can we find out if the dictionary is already 256? 
        # I think its : Headers Size = 286

        $SESSION.CURRENT_ARCHIVE.list_string_7z = Invoke-Expression '7z.exe l "$p"'

        $SESSION.CURRENT_ARCHIVE.list_string_7z | Format-Wide | Out-String

        $SESSION.CURRENT_ARCHIVE.file_count = ($SESSION.CURRENT_ARCHIVE.list_string_7z | Select-String -CaseSensitive -Pattern "^.+([0-9]+)\s{1,}files$").Matches.Groups[1]

        $SESSION.CURRENT_ARCHIVE.dict_size = ($SESSION.CURRENT_ARCHIVE.list_string_7z | Select-String -CaseSensitive -Pattern "^Headers\s{1,}Size\s{1,}\=([0-9]+)").Matches.Groups[1]

        # 2006-03-09 13:12:53             442324       177220  31 files, 38 folders
        $SESSION.CURRENT_ARCHIVE.dir_count = ($this.r.list_string_7z | Select-String -CaseSensitive -Pattern "^.+([0-9]+)\s{1,}folders$").Matches.Groups[1]

        ################

        if ($SESSION.CURRENT_ARCHIVE.dict_size -eq 256) {
            $SESSION.CURRENT_ARCHIVE.status = "skipped"
            $script:L.i(@("Archive already dict_size: 256 skipping..."))
            break
        }

        ######################

        if ($SESSION.CURRENT_ARCHIVE.file_count -eq 0) {
            $script:L.e(@("Archive: $(SESSION.CURRENT_ARCHIVE.Name)", " has ", "zero files"))
            break
        } else {
            $script:L.i(@("Archive: FileCount = ", $SESSION.CURRENT_ARCHIVE.file_count))
        }

        if ($SESSION.CURRENT_ARCHIVE.dir_count -eq 0) {
            $script:L.e(@("Archive: $(SESSION.CURRENT_ARCHIVE.Name)", " has ", "zero dirs"))
            break
        } else {
            $script:L.i(@("Archive: DirCount = ", $SESSION.CURRENT_ARCHIVE.dir_count))
        }

        $SESSION.CURRENT_ARCHIVE.destination = Join-Path `
            $CONFIG.paths.temp[$current_tmp_index] `
            $(New-Guid)

        $script:L.i(@("Current temporary dir: ", $SESSION.CURRENT_ARCHIVE.destination))
        # TODO: later on random maybe instead of sequential tmp

        # dictionary 256 makes sense for .exe not always so useful for non exe
        # however just use this... 

        $extract_opt = @{}
        $compress_opt = @{}

        $SESSION.CURRENT_ARCHIVE.extract = Invoke-Expression `
            '7z.exe x '+$src_path+' '+$extract_opt

        $SESSION.CURRENT_ARCHIVE.compress = Invoke-Expression `
            '7z.exe c '+$SESSION.CURRENT_ARCHIVE.destination+' '+$compress_opt  

        Start-Sleep -Seconds 15

        $current_tmp_index++
    }
}

CreateMissingDirectories
GetCompressedFilesFromRoot 
Write-Host Processing: $SESSION.eligible_files.Length Files...
ProcessCompressedFileListSlowly
