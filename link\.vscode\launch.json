{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Backup - Location - dryrun",
            "program": "${workspaceFolder}/rename-links-std.mjs",
            "request": "launch",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "type": "node",
            "args": ["${cwd}/test-tree/0", "--backup",  "--location", "${cwd}/test-tree/0/backup", "--dryrun"]
        },
        {
            "name": "Shorthand - Dry Run",
            "program": "${workspaceFolder}/rename-links-std.mjs",
            "request": "launch",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "type": "node",
            "args": ["${cwd}/test-tree/0", "-b",  "-l", "${cwd}/test-tree/0/backup", "-v", "-e", "-c", "-d"]
        },
        {
            "name": "Not Dry Run",
            "program": "${workspaceFolder}/rename-links-std.mjs",
            "request": "launch",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "type": "node",
            "args": ["${cwd}/test-tree/0", "--backup",  "--location", "${cwd}/test-tree/0/backup", "--confirm", "--verbose", "--debug"]
        }
    
    ]
}
