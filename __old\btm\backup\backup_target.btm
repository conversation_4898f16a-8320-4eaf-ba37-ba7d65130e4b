LOADBTM ON
BREAK ON
ECHO backup target %2% to archive name %3%

IF "%1" == "help" GOTO help
IF "%1" == "/h" GOTO help
GOTO startcommand

:help
ECHO Parameters:
ECHO.
ECHO	 1	target backup location
ECHO	 2	directory to backup
ECHO	 3	archive name
<PERSON>H<PERSON>	 4	"auto" for automated processes, ensuring is minimized
<PERSON>HO.
GOTO quit

:startcommand

IFF %_pipe != 0 .OR. %_transient != 0 .OR. "%4" == "auto" THEN & GOTO minwindow & ELSE & GOTO begin & ENDIFF

:minwindow
REM Minimize the backup
ECHO 
WINDOW MIN
GOTO begin

:begin
SETLOCAL

ECHO.

REM add location\archive name.rar location
crar a "%1%3.rar" "%2"

FREE
IFF %_pipe != 0 .OR. %_transient != 0 THEN & GOTO endcmd & ELSE & GOTO finish & ENDIFF
ENDLOCAL

:endcmd
EXIT

:finish
ECHO.
ECHO Finished backingup %time% on %date
FREE
ECHO.
GOTO quit

:quit
QUIT