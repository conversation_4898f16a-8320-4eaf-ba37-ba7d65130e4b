class $$CACHE {
    [Array]
    f= {
        // cache read streams still running from disk
        loading: false,
        // known cache data is read
		ready: true
    }
    constructor() {
        /// guard against multiple instances
    }
}
// .locations[k].cacheable or per locations
    /// this is best run by a worker

    /// 1. the cache builder works by reducing each dir,
        /// if the dir has a lot of files it has it owns json file
    
	/// 2. each entry is hashed using filename_hash, it contains the globbed file entries

	/// cluster / threaded mode?
	//$$CACHE._worker = async (path) => {}

    $$CACHE.path = path => `${getd('FLR_VAR_DATA')}\\CACHE\\${filename_hash(path)}`
        /// this will be a dir if it contains a large count
        /// this will be a json file if its small
    $$CACHE.has_cache_record = path => exists($$CACHE.path(filename_hash(path)))
	
	/// cache record
	$$CACHE.Record = () => ({p:'escaped path',dir_hash:'if path is dir hash of contents',filename_hash:'path hash',now:now()})
	/// a cache record for a path is collection d:[{
    // files:[],dirs:[],count: === files.length+ dirs.length
    //}]
		/// so we know where files live
	    /// we normalize into hashmap
    $$CACHE.builder = (path) => {
		// all files are added to current

		let dir_hash
		// the problem here is re doing the same dirhash for something you already did
		if (!(dir_hash=cache(filename_hash(path)))) {
			// spawn builder
		}

		// if path exists on disk 
			// load from disk json in chunks
				/// if the file is massive, async
		// if path has many 
			//many(path, 1000)
		// 
    }
	    /// find cache fragments from previous runs
        /// cache_store_paths.map((p,i,a) => {
		    /// for each cache_store_paths ( might from multiple versions ) ... load cache fragments
	    ///})
	$$CACHE.find = cache_store_paths => ({})
	/// load a cache fragment as pipeline
	$$CACHE.load = path => (pipeline())
        /// the cache is json files of all the entries for that path
    $$CACHE.is_ready = () => $$CACHE.f.ready
		/// eventually you can pause all other tasks and just load cache first using Signal : signal [pause]
	$$CACHE.is_loading = () => $$CACHE.f.loading



	//// v1 cache SETS:-> this just tells us where to look

	/// it knows when a path contains a kNOWN extension, if it matches once it stops
	/// startup will scan this whilst the other worker is going ...

		/// if the previous parent path segments exist on another disk used that
		/// 

		/// this data is then cached












// the cache hashing reduces words into numbers, but deterministically and equality such that a hex representation of the path chars, is always matching
// this way we can build on the string into number programmatically
// in v1 this cannot be changed

			/// data that was already generated:- ( this could be huge ) 

				/// this is a hashmap of strings -> that points to another collection and index :- this data might get really big
				/// if that happens turn it into huge text files... and then stream read to find

				/// $$CACHE.d[int rootfirstletter][{k,d}] 
					// is a key ==> value store that is a ref via $$CACHE.g()
					// no order is guranteed
				// first level has this always [0]
				// however if the counts are huge:-
					//// if $$CACHE.d[int rootfirstletter]...[j]
						//// if at a level a number exists, its the same record but stored as path_hashname data
						//// and this will have [subnumber] if its really big
							/// each .d[key] is a collection 

				

	/// none below was implemented
				/// data generated during processing needs:-

			/// TYPE: ARRAY index of keys, for each path first letter is the k
				/// $$CACHE.invalid ==> stale end of cycle garbage collect or last run
					// if file is in invalid, recache at end
					// this should always be zero as much as possible

				/// $$CACHE.valid ==> known loaded valid collections
					// if file mutation then make invalid

				/// $$CACHE.prop['common_prop']=['key to the .d{} hash map']
					/// used here for `path_hash`

			/// the indexes within the dir change depending on the entries
			/// the cache is a lot of big pathhash d//( stored as DETERMINISTIC CACHE HASH )// maps to ==> [0], [0][0]
			/// Record({
				//version: 1,
				
				// v2/3

				/// md5_path_hash
				/// cache_hash , always, same as parent key

				/// size, if feature enabled, the size on disk

			/// used in chunking entries feature
				/// count, of child entries
				/// flr_cache_entrychunksize ==> .0, .1, ..

				// v4 /// attribs, if feature enabled, the current mask/mode

				/// stat_hash, if the path_hash is a directory the hash of its stat object
				/// stat_obj

				/// count_hash, if the path_hash is a directory the hash of its children count

				/// children / entries ==> array of entries, if feature enabled

				/// parent_hash, path hash to the parent dir

				/// first_letter

			/// speedup processing within an inner loop
				//deleted: true

			//}) ==>
			//[cache_version],i,j.k

			// for these kind of algorithms delete thing[key] is quite expensi