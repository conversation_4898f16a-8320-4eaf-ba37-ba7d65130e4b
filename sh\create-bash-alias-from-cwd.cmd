@ECHO OFF

REM takes the current path, of any length, `i:\lib\local` and turns it into a bash alias of the form `i:lib:local` and appends it to the bash_profile file, first making a backup.

SETLOCAL EnableDelayedExpansion

SET SRC_BASH_PROFILE=C:\users\<USER>\.bash_profile
SET DST_BASH_PROFILE=I:\b\auto\proc\create-bash-alias-from-cwd\d\.bash_profile.current

REM 1. make a copy of the file to I:\b\auto\proc\create-bash-alias-from-cwd\d\.bash_profile.last

COPY /Y /V "%SRC_BASH_PROFILE%" "%DST_BASH_PROFILE%"

REM 2. Get the current cwd
SET CWD=%CD%

REM 3. Turn the cwd into a bash alias of the form i:lib:local using for for any length of path

SET ALIAS_NAME=
FOR %%i IN ("%CWD:\=:%" ) DO (
    SET ALIAS_NAME=!ALIAS_NAME!%%i:
)
SET ALIAS_NAME=!ALIAS_NAME:~0,-1!

REM 4. Check if the alias already exists in the bash profile
FINDSTR /C:"alias %ALIAS_NAME%" "%SRC_BASH_PROFILE%" > NUL
IF !ERRORLEVEL! EQU 0 (
    ECHO Alias already exists: %ALIAS_NAME%
    GOTO :EOF
)
ECHO Alias does not exist: %ALIAS_NAME%

REM 5. git bashes uses the path form of : /i/lib/local we have to convert a windows DOS style path to this format:-
SET CWD_GITBASH=%CWD:\=/%
ECHO %CWD_GITBASH%

REM 6. Append the alias to the bash profile
REM ECHO alias %ALIAS_NAME%='cd "%CWD_GITBASH%"' >> "%SRC_BASH_PROFILE%"

:EOF
