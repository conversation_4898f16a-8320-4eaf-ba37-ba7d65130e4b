@ECHO OFF
SETLOCAL ENABLEDELAYEDEXPANSION

::----------------------------------------------------------------------
:: Script: link_dirs.bat
:: Purpose: Creates directory symbolic links in a destination folder
::          for every directory found in a source folder.
:: Usage:   link_dirs.bat "C:\Path\To\Source\Folder" "C:\Path\To\Destination\Links"
:: Requires: Administrator privileges to run MKLINK.
::----------------------------------------------------------------------

REM --- Configuration ---
SET "SourceFolder=%~1"
SET "TargetFolder=%~2"

REM --- Input Validation ---
IF "%SourceFolder%"=="" GOTO Usage
IF "%TargetFolder%"=="" GOTO Usage

IF NOT EXIST "%SourceFolder%\" (
    ECHO ERROR: Source folder not found: "%SourceFolder%"
    GOTO EndScriptWithError
)

IF NOT EXIST "%TargetFolder%\" (
    ECHO ERROR: Target folder not found: "%TargetFolder%"
    ECHO You may need to create it first.
    GOTO EndScriptWithError
)

ECHO Starting directory linking process...
ECHO Source: "%SourceFolder%"
ECHO Target: "%TargetFolder%"
ECHO ==================================================

REM --- Main Processing Loop ---
FOR /D %%D IN ("%SourceFolder%\*") DO (
    SET "DirName=%%~nxD"
    SET "LinkPath=%TargetFolder%\!DirName!"
    SET "SourcePath=%%~fD"

    ECHO Checking for: "!DirName!"

    REM Check if a file or directory already exists at the target path
    IF EXIST "!LinkPath!" (
        ECHO WARNING: Item already exists at "!LinkPath!". Skipping creation.
    ) ELSE (
        ECHO   Creating link: "!LinkPath!" --^> "!SourcePath!"
        MKLINK /D "!LinkPath!" "!SourcePath!"
        IF !ERRORLEVEL! NEQ 0 (
            ECHO   ERROR: Failed to create link for "!DirName!". Check permissions or path validity.
        ) ELSE (
            ECHO   Successfully created link.
        )
    )
    ECHO.
)

ECHO ==================================================
ECHO Directory linking process finished.
GOTO EndScriptSuccess

:Usage
ECHO Creates directory symbolic links for all subdirectories from a source to a target folder.
ECHO.
ECHO Usage: %~nx0 "SourceFolderPath" "TargetFolderPath"
ECHO.
ECHO Example: %~nx0 "C:\My Music\Albums" "D:\Music Links"
ECHO.
ECHO Requires Administrator privileges.
GOTO EndScriptWithError

:EndScriptSuccess
ENDLOCAL
EXIT /B 0

:EndScriptWithError
ENDLOCAL
EXIT /B 1
