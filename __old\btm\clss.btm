LOADBTM ON
BREAK ON
ECHO building archives index.
ECHO.
ECHO v0.1
ECHO TODO : use lists and gosubs
ECHO cache lists in script temp?
ECHO.
SETLOCAL
ALIAS tree = *tree /A
REM (a)dd -(s)olid -(r)ecovery(r)ecord -(d)ictionary 4096k -(t)imeStamp -co(m)pressionLevel -(y)es -(f)ull (p)ath (d)rive
ALIAS winace = *winace a -s -rr -r -fpd -d4096 -tl -m5 -y
ALIAS rm = del /pwtr
SET listdir=h:\backup\auto\snapshot\lists\
SET filepre=%listdir%full_verbose-(%_DAY%-%_MONTH%-%_YEAR%)-
ECHO writing to file prefix: %filepre%
ECHO.

REM **1. SAMPLES

SET list_target=h:\audio\samples
SET	listname=001-audio_samples.txt
SET fullname=%filepre%%listname%
ECHO %fullname%
ECHO. > "%fullname%"
ECHO **1. SAMPLES >> "%fullname%"
ECHO. >> "%fullname%"

rem **** BEGIN COMMON 1 ****
IF EXIST "%@SUBSTR[%fullname%,0,%@EVAL[%@LEN[%fullname%]-4]]ace" DEL "%@SUBSTR[%fullname%,0,%@EVAL[%@LEN[%fullname%]-4]]ace"
rm %listdir%\*.*
TREE %list_target% >> "%fullname%"
SET drv=%@INSTR[0,1,%list_target%]
ECHO. >> "%fullname%"
ECHO DISK FREE EXTENDED INFO >> "%fullname%"
ECHO. >> "%fullname%"
FREE %drv%: >> "%fullname%"
ECHO list name: %fullname%
winace "%@SUBSTR[%fullname%,0,%@EVAL[%@LEN[%fullname%]-4]]" "%fullname%"
rem **** END COMMON ****

REM **2. ARCHIVES

SET list_target=g:\archives
SET	listname=002-software_archive.txt
SET fullname=%filepre%%listname%
ECHO %fullname%
ECHO. > "%fullname%"
ECHO **2. ARCHIVES >> "%fullname%"
ECHO. >> "%fullname%"

rem **** BEGIN COMMON 2 ****
IF EXIST "%@SUBSTR[%fullname%,0,%@EVAL[%@LEN[%fullname%]-4]]ace" DEL "%@SUBSTR[%fullname%,0,%@EVAL[%@LEN[%fullname%]-4]]ace"
rm %listdir%\*.*
ALIAS tree = *tree /A /S /F /t
TREE %list_target% >> "%fullname%"
SET drv=%@INSTR[0,1,%list_target%]
ECHO. >> "%fullname%"
ECHO DISK FREE EXTENDED INFO >> "%fullname%"
ECHO. >> "%fullname%"
FREE %drv%: >> "%fullname%"
ECHO list name: %fullname%
winace "%@SUBSTR[%fullname%,0,%@EVAL[%@LEN[%fullname%]-4]]" "%fullname%"
rem **** END COMMON ****

REM **3. MUSIC COLLECTION

SET list_target=h:\audio\music
SET	listname=003-music_collection-mp3_ogg_ape.txt
SET fullname=%filepre%%listname%
ECHO %fullname%
ECHO. > "%fullname%"
ECHO **3. MUSIC COLLECTION >> "%fullname%"
ECHO. >> "%fullname%"

rem **** BEGIN COMMON 3 ****
IF EXIST "%@SUBSTR[%fullname%,0,%@EVAL[%@LEN[%fullname%]-4]]ace" DEL "%@SUBSTR[%fullname%,0,%@EVAL[%@LEN[%fullname%]-4]]ace"
rm %listdir%\*.*
TREE %list_target% >> "%fullname%"
SET drv=%@INSTR[0,1,%list_target%]
ECHO. >> "%fullname%"
ECHO DISK FREE EXTENDED INFO >> "%fullname%"
ECHO. >> "%fullname%"
FREE %drv%: >> "%fullname%"
ECHO list name: %fullname%
winace "%@SUBSTR[%fullname%,0,%@EVAL[%@LEN[%fullname%]-4]]" "%fullname%"
rem **** END COMMON ****

ENDLOCAL