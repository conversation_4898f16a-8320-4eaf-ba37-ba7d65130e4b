/** for a directory `W:\a\s\pianobook\ds-v2` scan for all `dspreset` files and return this as a chalk pack formatted pretty list that alternatives in a cycle of colours that represent a waterfall.
 * If the user types a word filter the filter by this + .dspreset as a glob.
 * 
 */

import {chalkpack, registerChalkpack} from 'file://X:/projects/dev2/chalkpack/index.mjs'
registerChalkpack()

const DECENT_SAMPLE_HORDE = 'W:\\a\\s\\pianobook\\ds-v2'

const ARG_DEF = {
    search_term: { type: 'string', short: 's', allowPositionals: true },
    help: { type: 'boolean', short: 'h' } 
}

export function main() {

}

