LOADBTM ON
BREAK ON
ECHO Backup-Web Projects v0.1
IFF %_pipe != 0 .OR. %_transient != 0 .OR. %1 == "auto" THEN & GOTO startcmd & ELSE & GOTO begin & ENDIFF
:startcmd
ECHO %1
WINDOW MIN
GOTO begin
:begin
SETLOCAL

REM (a)dd -(s)olid -(r)ecovery(r)ecord -(d)ictionary 4096k -(t)imeStamp -co(m)pressionLevel -(y)es -(f)ull (p)ath (d)rive
ECHO Current backup path: %BACKUP%
ECHO.

crar a "%BACKUP%projects.rar" "i:\projects"

FREE
IFF %_pipe != 0 .OR. %_transient != 0 THEN & GOTO endcmd & ELSE & GOTO finish & ENDIFF
ENDLOCAL
:endcmd
EXIT
:finish
ECHO.
ECHO Finished backing up web projects at %time% on %date
FREE
ECHO.
QUIT