<?xml version="1.0" encoding="utf-8"?>
<FreeFileSync XmlType="BATCH" XmlFormat="23">
    <Notes/>
    <Compare>
        <Variant>TimeAndSize</Variant>
        <Symlinks>Exclude</Symlinks>
        <IgnoreTimeShift/>
    </Compare>
    <Synchronize>
        <Changes>
            <Left Create="right" Update="right" Delete="right"/>
            <Right Create="right" Update="right" Delete="right"/>
        </Changes>
        <DeletionPolicy>Versioning</DeletionPolicy>
        <VersioningFolder Style="TimeStamp-Folder" MaxAge="15">H:\b\ver\w.mirror</VersioningFolder>
    </Synchronize>
    <Filter>
        <Include>
            <Item>*</Item>
        </Include>
        <Exclude>
            <Item>\System Volume Information\</Item>
            <Item>\$Recycle.Bin\</Item>
            <Item>\RECYCLE?\</Item>
            <Item>*\thumbs.db</Item>
        </Exclude>
        <SizeMin Unit="None">0</SizeMin>
        <SizeMax Unit="None">0</SizeMax>
        <TimeSpan Type="None">0</TimeSpan>
    </Filter>
    <FolderPairs>
        <Pair>
            <Left>I:\e\dr\c\usr\bin</Left>
            <Right>I:\e\dr\c\v\w-mirror-r.pfo\c\usr\bin</Right>
            <Synchronize>
                <Changes>
                    <Left Create="right" Update="right" Delete="right"/>
                    <Right Create="right" Update="right" Delete="right"/>
                </Changes>
                <DeletionPolicy>Versioning</DeletionPolicy>
                <VersioningFolder Style="TimeStamp-Folder" MaxAge="90">H:\b\ver\w.mirror\c\usr\bin</VersioningFolder>
            </Synchronize>
        </Pair>
        <Pair>
            <Left>I:\e\dr\d\usr\bin</Left>
            <Right>I:\e\dr\c\v\w-mirror-r.pfo\d\usr\bin</Right>
            <Synchronize>
                <Changes>
                    <Left Create="right" Update="right" Delete="right"/>
                    <Right Create="right" Update="right" Delete="right"/>
                </Changes>
                <DeletionPolicy>Versioning</DeletionPolicy>
                <VersioningFolder Style="TimeStamp-Folder" MaxAge="90">H:\b\ver\w.mirror\d\usr\bin</VersioningFolder>
            </Synchronize>
        </Pair>
        <Pair>
            <Left>I:\proc</Left>
            <Right>I:\e\dr\c\v\w-mirror-r.pfo\proc</Right>
            <Synchronize>
                <Changes>
                    <Left Create="right" Update="right" Delete="right"/>
                    <Right Create="right" Update="right" Delete="right"/>
                </Changes>
                <DeletionPolicy>Versioning</DeletionPolicy>
                <VersioningFolder Style="TimeStamp-Folder" MaxAge="90">H:\b\ver\w.mirror\proc</VersioningFolder>
            </Synchronize>
        </Pair>
        <Pair>
            <Left>I:\projects</Left>
            <Right>I:\e\dr\c\v\w-mirror-h.pfo\projects\i</Right>
            <Synchronize>
                <Changes>
                    <Left Create="right" Update="right" Delete="right"/>
                    <Right Create="right" Update="right" Delete="right"/>
                </Changes>
                <DeletionPolicy>Versioning</DeletionPolicy>
                <VersioningFolder Style="TimeStamp-Folder" MaxAge="90">E:\b\ver\w.mirror\projects</VersioningFolder>
            </Synchronize>
        </Pair>
        <Pair>
            <Left>S:\b\mnt</Left>
            <Right>I:\e\dr\c\v\w-mirror-h.pfo\mnt\s</Right>
            <Synchronize>
                <Changes>
                    <Left Create="right" Update="right" Delete="right"/>
                    <Right Create="right" Update="right" Delete="right"/>
                </Changes>
                <DeletionPolicy>Versioning</DeletionPolicy>
                <VersioningFolder Style="TimeStamp-Folder" MaxAge="90">P:\b\ver\w.mirror\mnt\s</VersioningFolder>
            </Synchronize>
            <Filter>
                <Include>
                    <Item>*</Item>
                </Include>
                <Exclude>
                    <Item>*.pfo</Item>
                </Exclude>
                <SizeMin Unit="None">0</SizeMin>
                <SizeMax Unit="None">0</SizeMax>
                <TimeSpan Type="None">0</TimeSpan>
            </Filter>
        </Pair>
        <Pair>
            <Left>S:\srv</Left>
            <Right>I:\e\dr\c\v\w-mirror-r.pfo\srv</Right>
            <Synchronize>
                <Changes>
                    <Left Create="right" Update="right" Delete="right"/>
                    <Right Create="right" Update="right" Delete="right"/>
                </Changes>
                <DeletionPolicy>Versioning</DeletionPolicy>
                <VersioningFolder Style="TimeStamp-Folder" MaxAge="90">H:\b\ver\w.mirror\srv</VersioningFolder>
            </Synchronize>
            <Filter>
                <Include>
                    <Item>*</Item>
                </Include>
                <Exclude>
                    <Item>vm\</Item>
                </Exclude>
                <SizeMin Unit="None">0</SizeMin>
                <SizeMax Unit="None">0</SizeMax>
                <TimeSpan Type="None">0</TimeSpan>
            </Filter>
        </Pair>
        <Pair>
            <Left>I:\docs</Left>
            <Right>E:\b\sync\w.mirror\docs</Right>
            <Synchronize>
                <Changes>
                    <Left Create="right" Update="right" Delete="right"/>
                    <Right Create="right" Update="right" Delete="right"/>
                </Changes>
                <DeletionPolicy>Versioning</DeletionPolicy>
                <VersioningFolder Style="TimeStamp-Folder" MaxAge="90">P:\b\ver\w.mirror\docs</VersioningFolder>
            </Synchronize>
        </Pair>
        <Pair>
            <Left>U:\notes</Left>
            <Right>E:\b\sync\w.mirror\obsidian-notes</Right>
            <Synchronize>
                <Changes>
                    <Left Create="right" Update="right" Delete="right"/>
                    <Right Create="right" Update="right" Delete="right"/>
                </Changes>
                <DeletionPolicy>Versioning</DeletionPolicy>
                <VersioningFolder Style="TimeStamp-Folder" MaxAge="90">P:\b\ver\w.mirror\obsidian-notes</VersioningFolder>
            </Synchronize>
        </Pair>
        <Pair>
            <Left>E:\b\arc\emails</Left>
            <Right>T:\b\sync\arc.mirror\emails</Right>
            <Synchronize>
                <Changes>
                    <Left Create="right" Update="right" Delete="right"/>
                    <Right Create="right" Update="right" Delete="right"/>
                </Changes>
                <DeletionPolicy>Versioning</DeletionPolicy>
                <VersioningFolder Style="TimeStamp-Folder" MaxAge="90">E:\b\ver\arc.mirror\emails</VersioningFolder>
            </Synchronize>
        </Pair>
        <Pair>
            <Left>I:\org</Left>
            <Right>I:\e\dr\c\v\w-mirror-h.pfo\org</Right>
            <Synchronize>
                <Changes>
                    <Left Create="right" Update="right" Delete="right"/>
                    <Right Create="right" Update="right" Delete="right"/>
                </Changes>
                <DeletionPolicy>Versioning</DeletionPolicy>
                <VersioningFolder Style="TimeStamp-Folder" MaxAge="90">P:\b\ver\w.mirror\org</VersioningFolder>
            </Synchronize>
        </Pair>
        <Pair>
            <Left>I:\ref</Left>
            <Right>I:\e\dr\c\v\w-mirror-r.pfo\ref</Right>
            <Synchronize>
                <Changes>
                    <Left Create="right" Update="right" Delete="right"/>
                    <Right Create="right" Update="right" Delete="right"/>
                </Changes>
                <DeletionPolicy>Versioning</DeletionPolicy>
                <VersioningFolder Style="TimeStamp-Folder" MaxAge="90">H:\b\ver\w.mirror\ref</VersioningFolder>
            </Synchronize>
        </Pair>
        <Pair>
            <Left>I:\docs\CV</Left>
            <Right>E:\Gdrive\docs\CV</Right>
        </Pair>
    </FolderPairs>
    <Errors Ignore="true" Retry="0" Delay="5"/>
    <PostSyncCommand Condition="Completion"/>
    <LogFolder/>
    <EmailNotification Condition="Always"/>
    <GridViewType>Action</GridViewType>
    <Batch>
        <ProgressDialog Minimized="true" AutoClose="true"/>
        <ErrorDialog>Show</ErrorDialog>
        <PostSyncAction>None</PostSyncAction>
    </Batch>
</FreeFileSync>
