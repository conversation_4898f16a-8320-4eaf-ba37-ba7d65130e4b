LOADBTM ON
ECHO Backup-Audio Data ver 0.2
IFF %_pipe != 0 .OR. %_transient != 0 THEN & GOTO startcmd & ELSE & GOTO begin & ENDIFF

:startcmd
WINDOW MIN
GOTO begin

:begin
SETLOCAL
REM (a)dd -(s)olid -(r)ecovery(r)ecord -(d)ictionary 4096k -(t)imeStamp -co(m)pressionLevel -(y)es -(f)ull (p)ath (d)rive
ALIAS winace = *winace a -s -rr -r -fpd -d4096 -tl -m5 -y
ECHO Current backup path: %BACKUP%
ECHO.
CDD %BACKUP%
FREE
MOVE %BACKUP%\LAST\audio_data.ace %BACKUP%\PENULT
MOVE %BACKUP%\CURRENT\audio_data.ace %BACKUP%\LAST
CDD %BACKUP%\CURRENT
winace "audio_data" "h:\audio\data"
IFF %_pipe != 0 .OR. %_transient != 0 THEN & GOTO endcmd & ELSE & GOTO finish & ENDIFF
ENDLOCAL

:endcmd
EXIT

:finish
ECHO.
ECHO Finished backing up audio data at %time% on %date%
DIR
FREE
ECHO.
QUIT
