cd /i/lib/local/nih-plug
git pull origin
git submodule update --init --recursive
cd plugins

cd buffr_glitch
cargo xtask bundle buffr_glitch --release

cd ../crisp
cargo xtask bundle crisp --release

cd ../crossover
cargo xtask bundle crossover --release

cd ../loudness_war_winner
cargo xtask bundle loudness_war_winner --release

cd ../puberty_simulator
cargo xtask bundle puberty_simulator --release

cd ../safety_limiter
cargo xtask bundle safety_limiter --release

cd ../soft_vacuum
cargo xtask bundle soft_vacuum --release

cd ../spectral_compressor
cargo xtask bundle spectral_compressor --release



cd ../../target/bundled/
mkdir -p /c/Program\ Files/Common\ Files/vst3/nih-plugins
cp -Rv *.vst3 /c/Program\ Files/Common\ Files/vst3/nih-plugins/
