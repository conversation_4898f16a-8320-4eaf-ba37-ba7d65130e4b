ECHO.
ECHO ###### script started ######
ECHO.

<PERSON><PERSON> perhaps should split this into separate files
REM but thats not a mean script!

GOTO startCheck

REM *************************************************************************
REM UTILITY SUBROUTINES
REM *************************************************************************

:drawlineCr
ECHO.
CALL drawline.cmd
ECHO.
RETURN

REM *************************************************************************

:startCheck
IFF %_pipe != 0 .OR. %_transient != 0 THEN & GOTO start & ELSE & GOTO pushdir & ENDIFF

:pushdir
pushd .
GOTO start

REM *************************************************************************

:start
ECHO.
ECHO matts multi function super backup script :) 2006
ECHO.
ECHO^tautomatic backup script intended to run on 4NT
ECHO^tsetting action variable to backup_target.btm
ECHO.

GOSUB drawlineCr

ECHO importing shared variables
ECHO.
CALL backup_variables.btm
ECHO.

GOSUB drawlineCr

REM *************************************************************************

REM START /wait /low /min /inv /c "backup script" 

REM start parameters
REM	/c	close
REM	/inv	invisible
REM	/min	minimise
REM	/low	low priority
REM	/wait	for session to end

REM crar script parameters
REM	% 0	backup_target
REM	% 1	target backup location
REM	% 2	directory to backup
REM	% 3	archive name
REM	% 4	use the word auto, ensuring is minimized

REM for loop
REM	/h	supress processing of . and ..

REM if
REM	/i	ignore case

REM *************************************************************************
REM USER DATA - DOESNT WORK - NEED TO USE RUNAS COMMAND ( I THINK )
REM *************************************************************************

REM ECHO backup user data....
REM probably need to do this as runas.. as it doesnt seem to get rights
REM RUNAS /profile /user:matt %action% %i_collection% "%USERPROFILE%\IdeaProjects" ideaprojects
REM RUNAS /profile /user:matt %action% %i_collection% "%USERPROFILE%\Application Data\Thunderbird\Profiles" mozilla-thunderbird
REM RUNAS /profile /user:matt %action% %i_collection% "%USERPROFILE%\Application Data\Mozilla\Firefox\Profiles" mozilla-firefox

REM *************************************************************************
REM SCRIPT
REM *************************************************************************

GOSUB drawlineCr
ECHO important...
START /c /inv /low /min "%3" CALL %action% %j_collection% "i:\script" script

REM *************************************************************************
REM PROJECTS
REM *************************************************************************

GOSUB drawlineCr
REM problem here is that some dirs are huge! like cpp

ECHO --projects
ECHO for each directory in projects rar

I:\
CD\projects
FOR /a:d /h %%dir IN (*.*) START /c /wait /low /min /inv "" CALL %action% %j_collection% "i:\projects\%%dir" projects_%%dir

REM *************************************************************************
REM DOCUMENTs
REM *************************************************************************

GOSUB drawlineCr
CD\docs
ECHO for each directory in documents rar
ECHO --documents
FOR /a:d /h %%dir IN (*.*) START /c /wait /low /min /inv "" CALL %action% %j_collection% "i:\docs\%%dir" docs_%%dir auto

REM *************************************************************************
REM VERSION CONTROL DIRECTORY
REM *************************************************************************

REM GOSUB drawlineCr
REM ECHO.
REM ECHO this will backup the repository on j:\server\ver back to i:\ 
REM ECHO so all the object history is saved.
REM START /c /low /min /inv  "backup versioning" %action% %i_collection% "j:\server\ver" ver 
REM ECHO.

REM *************************************************************************
REM APP AND SETTINGS SNAPSHOT
REM *************************************************************************

REM ECHO backing up snapshot data
REM this is the old way
REM COPY /qguv e:\usr\bin\soulseek\*.cfg %i_snapshot%
REM COPY /qguv e:\usr\bin\soulseek\*.cfg %j_snapshot%

REM new way
REM CALL daily_snapshot.btm

REM *************************************************************************
REM DISK TREE INDEXES 
REM *************************************************************************

GOSUB drawlineCr
ECHO saving disk tree indexes

IF /i EXIST I:\__backup\snapshot\tree\c.txt DEL /y I:\__backup\snapshot\tree\c.txt
START /c /low /min /inv "tree c" TREE /b c:\ > I:\__backup\snapshot\tree\c.txt

GOSUB indexTree d
GOSUB indexTree e
GOSUB indexTree i
GOSUB indexTree j
GOSUB indexTree m

REM just doing this to be safe not sure if needed
GOTO endCheck

REM *************************************************************************
REM indexTree SUBROUTINE
REM *************************************************************************

:indexTree [drive]
IF /i EXIST I:\__backup\snapshot\tree\%drive%.txt DEL /y I:\__backup\snapshot\tree\%drive%.txt
START /c /low /min /inv /wait "tree %drive%" TREE /b %drive%:\ > I:\__backup\snapshot\tree\%drive%.txt
RETURN

REM *************************************************************************

REM add a if exist k: ( online ) backup bit



:endCheck
IFF %_pipe != 0 .OR. %_transient != 0 THEN & GOTO end & ELSE & GOTO popdir & ENDIFF

:popdir
popd .

:end

ECHO.
ECHO ###### script ended ######
ECHO.