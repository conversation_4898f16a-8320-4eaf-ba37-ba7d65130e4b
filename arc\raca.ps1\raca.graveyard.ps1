
# it would be faster in one loop instead we have two since we want to wait to give disk a rest 
# after each write
# https://stackoverflow.com/questions/16613656/how-can-i-check-if-a-file-is-older-than-a-certain-time-with-powershell
#
# [DateTime]::UtcNow | get-date -Format "yyyy-MM-ddTHH:mm:ssZ"

# from usbbackup.exe from mcmilk.de - 
# 7zg-mini a "%A" %o -m0=zstd -mx2 -ms=on -mhe -slp -ssc -ssw -scsWIN -p"%P" "%p"
# 7z a archiv.zst -m0=zstd -mx22
# idle

# 7zS.exe is a symbolic link to 7z Std

# 7z l archiv.zst  is bad but its the last line to see it 

####### ---- PROCESS ---- ENTRY

# plus $tempFolderPath = Join-Path $Env:Temp $(New-Guid)
# New-Item -Type Directory -Path $tempFolderPath | Out-Null

# ---- CREATE TEMPORARY DIRECTORIES
# $defaultTemporaryLocations | ForEach-Object

# ---- CREATE LOGS DIR

# ---- SEARCH FOR MATCHING FILES 
# Get-ChildItem

# However, the exclusions are applied after the inclusions, which can affect the final output.
# if $RECURSIVE
# if $FOLLOW
# -Attributes
# -Include
# -Exclude
# -FollowSymlink
# -Recursive

# use Where-Object to filter in situ and is faster
