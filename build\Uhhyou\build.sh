cd /i/lib/local/Uhhyou/src
cd VSTPlugins
echo
du -hcs .

cmake --version

# cmake -G"Visual Studio 16 2019" -A x64 -DSMTG_MYPLUGINS_SRC_PATH="../../VSTPlugins" -DSMTG_ADD_VST3_HOSTING_SAMPLES=FALSE -DSMTG_ADD_VST3_PLUGINS_SAMPLES=FALSE ..
# cmake --build . -j --config Release

rm -fR build/*

#cmake -S . -B build -G "Visual Studio 17 2022" -A Win64
cmake -S . -B build -G "Visual Studio 17 2022" -A x64

cmake --build build --config Release

#cd build/VST3/Release
#explorer.exe .
echo
du -hcs .
echo
