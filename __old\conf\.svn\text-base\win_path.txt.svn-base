e:\4nt;e:\usr;i:\script\conf;i:\script\cmd;i:\script\btm;i:\script\bat;i:\script\nt;e:\usr\bin;e:\usr\w32\bin;%SystemRoot%\system32;%SystemRoot%;%SystemRoot%\System32\Wbem;e:\usr\bin\TortoiseCVS;e:\usr\bin\xnview;e:\usr\bin\tidy;e:\usr\bin\xenu;e:\usr\bin\popcorn;e:\usr\bin\hydraIRC;e:\usr\bin\colorgen;"E:\Program Files\Norton Ghost 2003\";E:\Program Files\DiskeeperWorkstation\;E:\usr\bin\cvsnt;e:\usr\httpd\apache2\bin;E:\j2sdk_nb\j2sdk1.4.2\bin;e:\usr\ant;e:\usr\rk;

%SystemRoot%\system32;%SystemRoot%;%SystemRoot%\System32\Wbem;F:\Program Files\Executive Software\Diskeeper\;F:\usr\GTK\2.0\bin;F:\Program Files\Support Tools\;i:\script\cmd\;i:\script\conf;i:\script\btm;i:\script\bat;i:\script\lnk;i:\script\wsh;e:\4nt\;;f:\usr\bin\winscp\



PHP...

merge module for filelist....


if %filename does not exist delete from list
if %filename matches entry in filelist delete



