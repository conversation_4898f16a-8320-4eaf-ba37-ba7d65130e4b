Write-Host "SETTING SERVICES STATUS"

Write-Host "  Disabled Services:"

Write-Host "    Data Usage "
Set-Service -Name DusmSvc -StartupType Disabled

Write-Host "    Diagnostic Execution Service "
Set-Service -Name diagsvc -StartupType Disabled

Write-Host "    Diagnostic Policy Service "
Set-Service -Name DPS -StartupType Disabled

Write-Host "    Diagnostic Service Host"
Set-Service -Name WdiServiceHost -StartupType Disabled

Write-Host "    Diagnostic System Host"
Set-Service -Name WdiSystemHost -StartupType Disabled

Write-Host "    Distributed Link Tracking Client"
Set-Service -Name TrkWks -StartupType Disabled

Write-Host "    Encrypting File System (EFS)"
Set-Service -Name EFS -StartupType Disabled

Write-Host "    Microsoft App-V Client"
Set-Service -Name AppVClient -StartupType Disabled

Write-Host "    Microsoft Keyboard Filter"
Set-Service -Name MsKeyboardFilter -StartupType Disabled

Write-Host "    Microsoft Store Install Service"
Set-Service -Name InstallService -StartupType Disabled

Write-Host "    Mobile_Series"
Set-Service -Name Mobile_Series -StartupType Disabled

Write-Host "    Net.Tcp Port Sharing Service"
Set-Service -Name NetTcpPortSharing -StartupType Disabled

Write-Host "    OpenSSH Authentication Agent"
Set-Service -Name ssh-agent -StartupType Disabled

Write-Host "    Cellular Time"
Set-Service -Name autotimesvc -StartupType Disabled

Write-Host "    Remote Registry"
Set-Service -Name RemoteRegistry -StartupType Disabled

Write-Host "    Retail Demo Service"
Set-Service -Name RetailDemo -StartupType Disabled

Write-Host "    Routing and Remote Access"
Set-Service -Name RemoteAccess -StartupType Disabled

Write-Host "    SSDP Discovery"
Set-Service -Name SSDPSRV -StartupType Disabled

Write-Host "    User Experience Virtualization Service"
Set-Service -Name UevAgentService -StartupType Disabled

Write-Host "    Windows Media Player Network Sharing Service"
Set-Service -Name WMPNetworkSvc -StartupType Disabled

Write-Host "    Xbox Accessory Management Service"
Set-Service -Name XboxGipSvc -StartupType Disabled

Write-Host "    Xbox Live Auth Manager"
Set-Service -Name XblAuthManager -StartupType Disabled

Write-Host "    Xbox Live Game Save"
Set-Service -Name XblGameSave -StartupType Disabled

Write-Host "    Xbox Live Networking Service"
Set-Service -Name XboxNetApiSvc -StartupType Disabled


Write-Host "  Manual Services:"
