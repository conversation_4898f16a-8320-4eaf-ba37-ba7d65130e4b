/** 
 * This file runs FL stores a hash of the FLP, and verifies ( or links ) valid paths :- 
 * samples and plugins
 */ 

// 1p
import assert from 'node:assert'
import crypto from 'node:crypto'
import { fileURLToPath } from 'node:url'
import { env } from 'node:process'
import fs from 'node:fs/promises'
import {createReadStream, createWriteStream,watch } from 'node:fs'
import {pipeline} from 'node:stream/promises'
import { parseArgs } from 'node:util'
import * as nodePath from 'node:path'
import { createInterface } from 'node:readline'
import { performance } from 'node:perf_hooks'
const { eventLoopUtilization } = performance

// 2p 
// used to identify useful strings
import { PLUGINS_LOCATIONS } from 'file://I:/pj/myne3/lib/os-win/plugins_locations.mjs' // with { type: "json" }
// change path sep to other platform or correct way around
const normalize_path = filePath => filePath
    .replace('\\\\','\\')
    .split(nodePath.sep === '\\' ? '/' : '\\')
    .join(nodePath.sep)

// Replace invalid characters with an hyphen, for windows os and standard on all
const to_safe_file_name = input => {
    return input.replace(/[<>:"/\\|?*\x00-\x1F]/g, '-').slice(0, 255).trim()
}

const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms))


// 3p 
import {execa} from 'execa'
import {default as gitRev} from 'git-rev'
import {default as ws} from 'windows-shortcuts'
import chalk from 'chalk'
import ora from 'ora'
import Configstore from 'configstore'
import * as tar from 'tar'
import { table } from 'table'
import {default as isTar} from 'is-tar'
import { rimraf } from 'rimraf'

let OUT = {
    log: [],    // array of actions taken sequentially for human log
	d: {
        ELU: [],
        MEM: [],
        args: {},
        ARG_DEF: {

            src: {
                type: 'string',
                short: 's'
            },

            ram: {
                type: 'boolean',
                short: 'r',
                default: true
            },

            clearRam: {
                type: 'boolean',
                default: false
            },

            plugins: {
                type: 'boolean',
                short: 'p',
                default: false
            },

            // this will take longer as it will skip de-noising
            // 'skip-nonsense': {
                // type: 'boolean',
                // default: false
            // },

            // for samples this copy them 
            copyFiles: {
                type: 'boolean',
                short: 'c',
                default: false
            },

                /// when debug it will log the decisions 
                debug: {
                    type: 'boolean',
                    short: 'd',
                    default: false
                },

            verbose: {
                type: 'boolean',
                short: 'v',
                default: false
            },
        },

        LOG_PREFIX: 'FLR >',

		lines: [
            // {d:{},f:{}}
        ],
        processed:[],
	    non_identified: [], // not sure
		
        missing: [], // missing items
        existing: [],

        // then ...
        samples: [], // samples found and existing
		plugins: [], // plugins found and existing

        ///  any references to a .mid file... on your disks
        midi: [],

        started: performance.now(),
        ended: 0,
        took: 0
	},
	md: {
		number_of_hashes: 0, // report number of hash files in store
		src_flp_hash: '',
		existing_hash : '',
		git_hash: ''
	},
	p: {
        SCRIPT_URL : import.meta.url,

		SRC_FLP: '',
		FLP_HASH_PATH: ''
	},
	f: {
	},
    STATIC_PATHS: {}
}
const show_last_elu = () => eventLoopUtilization(OUT.d.ELU[OUT.d.ELU.length-1].elu).utilization
const show_memory_usage = () => process.memoryUsage().rss
OUT.d.ELU.push({name: 'module',elu:eventLoopUtilization(),now: Date.now()})
OUT.d.DAW = {
    // enum of possible supported daws
    name: ['Ableton', 'Bitwig', 'FL studio']
}
// *** location of variable data ***
/// job data each time it ran 
    OUT.STATIC_PATHS.I_PROC_VAR_D_FLRUN_JOB_DATA = 'I:\\var\\d\\fl-run\\job-data',

/// the location of the symlinks to the moved files
    OUT.STATIC_PATHS.MOVED_FILE_LINKS_SYMLINK_DIR = 'I:\\var\\d\\fl-run\\mfl\\sls',

/// the location of the copied file store
    OUT.STATIC_PATHS.COPIED_FILE_STORE_DIR = 'I:\\var\\d\\fl-run\\mfl\\cfs',

/// the file name with the last hash
    OUT.STATIC_PATHS.I_PROC_VAR_D_FLRUN_D_HASH = 'I:\\var\\d\\fl-run\\hash',
    
/// for the path hash, the output of strings2 raw
    OUT.STATIC_PATHS.I_PROC_VAR_D_FLRUN_D_STR_RAW = 'I:\\var\\d\\fl-run\\s2\\raw',
    
/// the output of the stripped lines so the main processing is quicker
    OUT.STATIC_PATHS.I_PROC_VAR_D_FLRUN_D_STR_STRIPPED = 'I:\\var\\d\\fl-run\\s2\\stripped',
    
/// for the path hash, the output of processing with what it thinks are the valid paths
    OUT.STATIC_PATHS.I_PROC_VAR_D_FLRUN_D_STR_OUT = 'I:\\var\\d\\fl-run\\s2\\out',

/// auto created:-
    // this is used so that the processing window is transparent, formatted, and the correct size 
    // - we start the fl process supplying argument to the explorer with the shortcut?
    OUT.STATIC_PATHS.FL_RUN_SHORTCUT= 'I:\\proc\\au\\fl-run\\lib\\lnk\\fl-runner.lnk',
    
/// current fl version used
    OUT.STATIC_PATHS.FL_PATH= 'D:\\a\\red\\prod\\2024\\24.2.2.4597',

/// path to links that the browser will find early so that we can stop the stupid searching
    OUT.STATIC_PATHS.MOVED_FILE_LINKS_SYMLINK_DIR= 'I:\\e\\run\\fl-run\\mfl\\sls',

/// path to the RAM DRIVE COPY OF FL STUDIO
    OUT.STATIC_PATHS.RAM_DRIVE_FL_STUDIO = 'B:\\t\\proc\\fl-ram\\d\\bin-24.2.2.4597',

/// path to your user dir documents
    //OUT.STATIC_PATHS.DOCUMENTS= `$HOME\\Documents`

// path to the start binary
    OUT.STATIC_PATHS.FLBIN_PATH = `${OUT.STATIC_PATHS.FL_PATH}\\FL64.exe`,

// path to human logs so you can see what it did
    OUT.STATIC_PATHS.HUMAN_LOG = `${OUT.STATIC_PATHS.DOCUMENTS}\\fl-run\\human`

// watch('./tmp', { encoding: 'buffer' }, (eventType, filename) => {
//   if (filename) {
//     console.info(filename);
//     // Prints: <Buffer ...>
//   }
// });
// fix up static paths
let watched=[]
Object
.keys(OUT.STATIC_PATHS)
.forEach(async (c,i,a) => {
    if (c.includes('$HOME')) {
        c= c.replace('$HOME', env.HOME)
        OUT.STATIC_PATHS[c] = c
    }
    try {
        await fs.access(OUT.STATIC_PATHS[c])
    } 
    catch (err) {
        console.info(OUT.d.LP, 'script: creating dir: ', OUT.STATIC_PATHS[c])
        OUT.log.push(`created dir: ${OUT.STATIC_PATHS[c]}`)
        await fs.mkdir(OUT.STATIC_PATHS[c], { recursive: true })
    }
    OUT.log.push(`watching: ${OUT.STATIC_PATHS[c]}`)
    watch(OUT.STATIC_PATHS[c], async (eventType, filename) => {
        try{
            const stats = await fs.stat(OUT.STATIC_PATHS[c]+nodePath.sep+filename)
            console.info(OUT.d.LP, `--> watch ${c}: ${eventType}::`, filename, stats.isFile() ? 'file' : 'dir', 'size: ', stats.size)
            OUT.log.push(`watch ${c}: ${eventType}:: ${filename} ${stats.isFile() ? 'file' : 'dir'} size: ${stats.size}`)
        }
        catch (err) {
            console.error(OUT.d.LP, 'watch: ', err)
            OUT.log.push(`watch: ${err}`)
            return
        }
        if (!watched.includes(filename)) {
            watched.push(filename)
        }
        console.info(OUT.d.LP, 'script: watching: ', OUT.STATIC_PATHS[c])
        return
    })
}) 
OUT.d.LP = OUT.d.LOG_PREFIX+'::'
let {values, positionals} = parseArgs({options: OUT.d.ARG_DEF, allowPositionals:false})
OUT.d.args = {...values,...positionals}
if (OUT.d.args.clearRam) {
    console.info(OUT.d.LP, 'clearing ram drive')
    //await fs.rm(OUT.STATIC_PATHS.RAM_DRIVE_FL_STUDIO, { recursive: true, force: true })
    // instead of dangerous rm rename teh folder
    await fs.rename(OUT.STATIC_PATHS.RAM_DRIVE_FL_STUDIO, OUT.STATIC_PATHS.RAM_DRIVE_FL_STUDIO+'-old')
    process.exit(0)
}
OUT.p.SRC_FLP = OUT.d.args.src
console.info(OUT.d.LP,'args:')
console.table(OUT.d.args)
if (OUT.d.args.debug) console.log('')
if (OUT.d.args.debug) console.info(OUT.d.LP, 'script: watching: ')
if (OUT.d.args.debug) console.table(watched)
if (OUT.d.args.debug) console.log('')
OUT.p.env_path = env.PATH.split(';')
OUT.p.SCRIPT_HOME = fileURLToPath(OUT.p.SCRIPT_URL)
console.info(OUT.d.LP,'script home: ', OUT.p.SCRIPT_HOME)

/// it needs no arguments for what we need here since it needs to be fast
/// type abcd.exe | strings2 | findstr /i SearchForThisString
OUT.p.STRINGS2_COMMAND = OUT.p.SCRIPT_HOME+'\\lib\\strings2\\strings2.exe'

//OUT.p.FD_COMMAND = OUT.p.SCRIPT_HOME+'\\lib\\fd\\fd.exe'
OUT.p.FD_COMMAND = 'fd'

try {
    await fs.access(OUT.p.STRINGS2_COMMAND)
    console.info(OUT.d.LP, 'strings2.exe found in lib')
} 
catch (err) {
    console.warn(OUT.d.LP, 'strings2.exe not found in lib, will search PATH')
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'env_path: ', OUT.p.env_path)
    OUT.p.STRINGS2_COMMAND = 'strings2.exe'
    // if for some reason the lib bundled string2 is missing then 
    // check strings2 is in the environment by searching env path location until its found
    for(const path of OUT.p.env_path) {
        let full_path = path + '\\' + OUT.p.STRINGS2_COMMAND
        try {
            await fs.access(full_path)
            console.info(OUT.d.LP, 'strings2.exe found in PATH: ', full_path)
            OUT.p.STRINGS2_COMMAND = '"'+full_path+'"'
            break
        } 
        catch (err) {
            /// if its not found then search every other ENV variable for it...
            console.warn(OUT.d.LP, 'strings2.exe not found in PATH: ', full_path)
            OUT.log.push(`strings2.exe not found in PATH: ${full_path}`)
        }
    }

    if (!OUT.p.STRINGS2_COMMAND) {
        Object
        .keys(env)
        .forEach(async e => {
            if (e === 'PATH') return
            full_path = nodePath.resolve(normalize_path(e + nodePath.sep + OUT.p.STRINGS2_COMMAND))
            try {
                await fs.access(full_path)
                console.info(OUT.d.LP, 'strings2.exe found in PATH: ', full_path)
                OUT.p.STRINGS2_COMMAND = '"'+full_path+'"'
                return
            } 
            catch (err) {
            }
        })
        if (!OUT.p.STRINGS2_COMMAND) {
            console.error(OUT.d.LP, 'strings2.exe not found in PATH or ENV, exiting...')
            OUT.log.push('strings2.exe not found in PATH or ENV, exiting...')
            process.exit(1)
        }
    }
}

// 1. It takes from the launching application the argument of the FLP.
// first create the MOVED_FILE_LINKS_SYMLINK_DIR dir if don't exist

const dir_hash = async (dir) => {
    const hash = crypto.createHash('md5')
    const files = await fs.readdir(dir)
    for await (const file of files) {
        const file_path = nodePath.resolve(dir, file)
        const stats = await fs.stat(file_path)
        if (stats.isFile()) {
            const data = await fs.readFile(file_path)
            hash.update(data)
        }
        else if (stats.isDirectory()) {
            hash.update(await dir_hash(file_path))
        }
    }
    return Promise.resolve(hash.digest('hex').toString('utf8'))
}

    /// symlink dir or file 
const link_path = async (from, to) => {
    const to_type = (await fs.stat(to)).isDirectory() ? 'dir' : 'file'
    console.info(OUT.d.LP, 'link_path: ', from, 'to: ', to, 'type: ', to_type)
    try {
        await fs.access(to)
        console.info(OUT.d.LP, 'link_path: exists', to)
        OUT.log.push(`link exists: ${to}`)
        return
    } 
    catch (err) {
        console.info(OUT.d.LP, 'link_path: creating', to)
        OUT.log.push(`creating link: ${to}`)
        await fs.symlink(from, to, to_type)
    }
    return Promise.resolve()
}

	/// for a file get a md5 hash
const get_hash_file = async path => {
    const md5_hash = crypto.createHash('md5')
    const data = await fs.readFile(path)
    md5_hash.update(data)
    const hash = md5_hash.digest('hex')
    console.info(OUT.d.LP, 'get_hash_file: hash: ', hash, 'for: ', path)
    OUT.log.push(`hash: ${hash} for: ${path}`)
    return hash
}

	/// for a file create a hash in the store
const create_hash_file = async p => {
    const hash = await get_hash_file(p)
    const write_filename = nodePath.basename(p)
    console.info(OUT.d.LP, 'create_hash_file: hash: ', hash, 'for: ', write_filename)
    let final_write_filename = OUT.STATIC_PATHS.I_PROC_VAR_D_FLRUN_D_HASH+'\\'+write_filename
    OUT.log.push(`wrote hash: ${hash} for: ${write_filename} as ${final_write_filename}`)
    await fs.writeFile(final_write_filename, hash)
}
	/// check if path has a hash
const has_hash = async file_path => {
    const write_filename = nodePath.basename(file_path)
    const hash_path = OUT.STATIC_PATHS.I_PROC_VAR_D_FLRUN_D_HASH+'\\'+write_filename
    let hash_exists = false
    try {
        hash_exists = await fs.access(hash_path)
        console.info(OUT.d.LP, 'has_hash: ', hash_exists, 'for: ', hash_path)
        OUT.log.push(`hash exists: ${hash_exists} for: ${hash_path}`)
        return hash_exists
    } 
    catch (err) {
        console.info(OUT.d.LP, 'has_hash: ', false, 'for: ', hash_path)
        OUT.log.push(`hash does not exist for: ${hash_path}`)
        return false
    }
    return Promise.resolve()
}
// ------------------------------------------------------------------
let VALID = {
    /// strings on single line, within xml, within noise, or like this:-
    // %FLStudioFactoryData%\Data\Patches\Packs\Legacy\Drums\FPC\Toms\FPC_Tom_GtomLow_001.wav
    samples: {
        ext: ['wav','mp3','ogg','flac','midi','midi'],
        drive_root: ['c','d','e','f','j','h','k','m','p','r','u','w','x'],
        sample_stores: [
            'a\\s\\f',
            'a\\coll',
            'a\\s\\coll',
            //'a\\s\\nf',
            'a\\s\\red'
        ],
        other_root: [
            '%FLStudioFactoryData%',
            '%FLStudioDownloadData%'
        ],
    },
    /// strings will appear like this:- 
        // F:\a\vstpluginsrepo\free.new\AirWindows\Distance364.dll8 ( or without )
    plugins: {
        dirs: [
            ...PLUGINS_LOCATIONS.vst_std, 
            ...PLUGINS_LOCATIONS.vst_local
        ],
        ext: ['clap','dll','vst3','mtpreset'],
            /// symlinked things might be here for now it just uses the above dirs
        root: ['c','d','f','j','h','k','p','r','s']
    }
}
// this is used to parse each line within the pipe output of strings2
// A:\ , a:\
VALID.RE_DRIVE = new RegExp('(?<drive>[A-Z]{1})\:\\\\','gi')

// Defines a pattern for a Windows path starting with a drive letter.
// Example matches: "C:\foo\bar\", "a:\Program Files\app.exe"
// - (?<drive>[A-Z]): Captures the drive letter (e.g., "C"). Case-insensitive due to 'i' flag.
// - :\\\\: Matches the literal ":\" (backslash is escaped).
// - (?<path>[^<>:"/|?*\\x00-\\x1F]+): Captures the rest of the path following "DRIVE:\".
//   - [^<>:"/|?*\\x00-\\x1F]+: Matches one or more characters that are NOT Windows forbidden path characters (<, >, :, ", /, \, |, ?, *) or ASCII control characters (0x00-0x1F).
//     This allows spaces, periods, hyphens, underscores, and backslashes (as separators) within the path string itself.
VALID.RE_PATH = new RegExp('(?<drive>[A-Z]):\\\\(?<path>[^<>:"/|?*\\x00-\\x1F]+)','gi')

// line by itself
VALID.RE_PATH_STANDALONE = new RegExp('^'+VALID.RE_PATH.source+'$','gi')

// this RE matches a path within some other valid text
VALID.RE_PATH_WITHIN_TEXT = new RegExp('.*'+VALID.RE_PATH.source+'.*','gi')

// we only need to find ascii alphabet chars for windows paths and plugins
VALID.RE_LINE_INTERESTED_CHARS = new RegExp('[a-z]+','gi')

// 2.2 otherwise it process strings2 text output: this is a pipe of strings terminating with CR

// 3. It processes this with strings2 to get a pipe of possibly useful fragments.
	// 3.1 most of the strings2 output will be garbage
	// 3.1.1 if the line doesn't contain a-z and characters that are for paths, xml, json, 
	// then it can be passed
	// REM ******* there should be a switch to compare the results with the above turned off
	// 3.2 for each line it looks for string that begins with `[A-Z]:\` and validates this is a path
// 5. the candidates are then tested to see if file exists or not. 
// exists and show this in two colours. green -> [U:\a\coll] red -> [foo\bar] using chalk
async function path_exists(p) {
    try {
        await fs.access(p)
        console.info(OUT.d.LP,'path_exists: exists', p)
        return true
    } 
    catch (err) {
        console.info(OUT.d.LP,'path_exists: does not exist', p)
        return false
    }
    return Promise.resolve()
}

async function process_line(line, counter) {
    // set a timer:
    const process_line_started = performance.now()
    OUT.d.ELU.push({name: 'process_line', elu:eventLoopUtilization(),now: Date.now()})
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_line: ELU: ', show_last_elu())
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_line: MEM: ', show_memory_usage())
    OUT.d.MEM.push(process.memoryUsage().rss)
    if(OUT.d.args.verbose) console.info(OUT.d.LP, 'process_line: length', line.length)
	//const ix = ++OUT.counters.line
	const line_out = {
		no: counter,
        d: {path: '',line, full_path: []},
		f: {exists: false, has_path: false, is_path_standalone: false, is_path_within_txt: false, is_line_garbage: false}
    }
    if (line.length === 0) return
    line = ''+line
        .trimStart()
        .trimEnd()

    const is_interesting = VALID.RE_LINE_INTERESTED_CHARS.test(line)
    // first check if the line contains valid characters
    if (!is_interesting) {
        if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_line: no interesting chars', line)
        return
    }
    else {
        if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_line: interesting chars', line)
		
        // only store the decisions
        line_out.f.has_path = false
		line_out.f.is_path_standalone = false
		line_out.f.is_path_within_txt = false
		line_out.f.is_line_garbage = false
        let match = null

		// check the path type
        if (VALID.RE_PATH_STANDALONE.test(line)) {
            match = [...line.matchAll(VALID.RE_PATH_STANDALONE)]
            if (match && match?.length) {
                if (OUT.d.args.debug) console.info(OUT.d.LP, 'process_line: match?.length>1')
                OUT.log.push(`multiple paths in line: ${line}`)
                match.forEach(m => {
                    if (OUT.d.args.debug) console.info(OUT.d.LP, 'process_line: path in line', m.groups.drive, m.groups.path)
                    OUT.log.push(`path in line: ${m.groups.drive}:${m.groups.path}`)
                    line_out.f.has_path = true
                    line_out.f.is_path_within_txt = true
                    line_out.d.drive = m.groups.drive
                    line_out.d.path_suffix = m.groups.path
                    const final_path = m.groups.drive + ':\\\\' + m.groups.path
                    if (OUT.d.args.debug) console.info(OUT.d.LP, 'process_line: final_path', final_path)
                    line_out.d.full_path.push(final_path)
                })
            }
        } 
        else if (VALID.RE_PATH_WITHIN_TEXT.test(line)) {
            match = [...line.matchAll(VALID.RE_PATH_WITHIN_TEXT)] // This regex uses RE_PATH.source
            if (match && match?.length) {
                if (OUT.d.args.debug) console.info(OUT.d.LP, 'process_line: match?.length>1')
                OUT.log.push(`multiple paths in line: ${line}`)
                match.forEach(m => {
                    if (OUT.d.args.debug) console.info(OUT.d.LP, 'process_line: path in line', m.groups.drive, m.groups.path)
                    OUT.log.push(`path in line: ${m.groups.drive}:${m.groups.path}`)
                    line_out.f.has_path = true
                    line_out.f.is_path_within_txt = true
                    line_out.d.drive = m.groups.drive
                    line_out.d.path_suffix = m.groups.path
                    const final_path = m.groups.drive + ':\\\\' + m.groups.path
                    if (OUT.d.args.debug) console.info(OUT.d.LP, 'process_line: final_path', final_path)
                    line_out.d.full_path.push(final_path)
                })
            }
        }

        // when showing line if its great than 90 chars, show the end truncated where the plugin is .dll, or .vst or .wav etc is likely to be
        const friendly_log_line = line.length > 90 ? line.slice(0, 90) + '...' : line

        if (!line_out.f.has_path) {
            if (OUT.d.args.verbose) console.warn(OUT.d.LP, 'process_line: decided is garbage, has_path: ', line_out.f.has_path)
            OUT.log.push(`decided is garbage: ${friendly_log_line}`)
            // if not existing
            if (!OUT.d.non_identified.includes(line)) {
                OUT.d.non_identified.push(line) 
            }
            else {
                if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_line: already non identified', friendly_log_line)
            }
            line_out.f.is_line_garbage = true
        }

        if (line_out.f.has_path) {
            if (OUT.d.args.verbose) console.info(OUT.d.LP,`process_line: decided: ${friendly_log_line} is a path`)

            if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_line: full_path.length before unique', line_out.d.full_path.length)
            // line_out.d.full_path = line_out.d.full_path
            //     .sort()
            //     // make the series unique
            //     .reduce(
            //         (acc, current) => {
            //             if (acc.length === 0 || acc[acc.length - 1] !== current) {
            //                 acc.push(current)
            //             }
            //             return acc
            //         },
            //         []
            //     )
            OUT.log.push(`made paths unique paths: ${line_out.d.full_path.length}`)
            if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_line: full_path.length after unique', line_out.d.full_path.length)

            line_out.d.full_path.forEach(async (current_path, i, a) => {

                let o = {d:{},f:{},p:{path: current_path.trimEnd()}}
                o.p.path = nodePath.normalize(nodePath.resolve(o.p.path))
                if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_line: full_path', o.p.path, 'i:', 'i')
                // the ends of paths can have numbers on the end that are not noise remove them :-
                // unless it is .vst3
                // o.p.path = o.p.path.endsWith('.vst3') 
                //     ? o.p.path 
                //     : o.p.path.replace(/(?:\s*\d+)+$/,'')
                // if the path has a 8 or one number that is not 3 remove it :-
                o.p.current_end_number = o.p.path.match(/[\d]+$/)
                if (o.p.current_end_number) {
                    if (o.p.current_end_number[0] === '8' && o.p.current_end_number[0] !== '3') {
                        o.p.path = o.p.path.replace(/[\d]+$/,'')
                    }
                }
                // if the last char is still `8` remove it 
                if (o.p.path.endsWith('8')) {
                    o.p.path = o.p.path.replace(/8$/,'')
                }
                o.p.drive = o.p.path.split(':\\')[0]
                o.p.current_extension = o.p.path.split('.').pop()
                o.p.current_drive_letter = o.p.drive.toLowerCase() // For case-insensitive comparison
                o.p.current_path_suffix = o.p.path.split(':\\')[1]

                if (OUT.d.args.verbose) console.info(OUT.d.LP, 'drive: ', o.p.current_drive_letter, 'path_suffix: ', o.p.current_path_suffix)
                if (OUT.d.args.verbose) console.info(OUT.d.LP, 'current_extension: ', o.p.current_extension)
                //console.info(OUT.d.LP, 'current_path_suffix: ', o.p.current_path_suffix)
                o.p.full_path_to_check = nodePath.resolve(nodePath.normalize(o.p.current_drive_letter + ':\\' + o.p.current_path_suffix))

                
                // if the path has been seen before then skip it
                if (OUT.d.existing.includes(o.p.path)) {
                    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_line: already exists', o.p.path)
                    OUT.log.push(`already exists: ${o.p.path}`)
                    return
                }

                // first decide if sample or plugin by testing the VALID stanzas
                o.f.is_sample = false
                o.f.is_plugin = false
                o.f.is_midi = (o.p.current_extension === 'mid' || o.p.current_extension === 'midi')

                try{
                    o.f.is_dir = (await fs.stat(o.p.full_path_to_check)).isDirectory()
                    o.f.is_file = (await fs.stat(o.p.full_path_to_check)).isFile()
                }
                catch(err) {
                    o.f.is_dir = false
                    o.f.is_file = false
                }

                if (OUT.d.args.debug) console.info(OUT.d.LP, 'is_sample: ', o.f.is_sample, 'is_plugin: ', o.f.is_plugin, 'is_midi: ', o.f.is_midi, 'is_dir: ', o.f.is_dir, 'is_file: ', o.f.is_file)

                o.f.sample_has_drive_root = (
                    VALID.samples.drive_root.includes(o.p.current_drive_letter))
                o.f.sample_has_other_root = (
                    VALID.samples.other_root.some(root => o.p.full_path_to_check.startsWith(root)))
                o.f.sample_has_valid_extension = (
                    VALID.samples.ext.some(ext => o.p.current_path_suffix.endsWith('.' + ext)))

                if(OUT.d.args.plugins) {
                    o.f.plugin_has_drive_root = (
                        VALID.plugins.root.includes(o.p.current_drive_letter)
                    )
                    o.f.plugin_has_valid_path = false
                    //VALID.plugins.dirs.some(dir => {
                        const path_without_last_part = o.p.full_path_to_check.split('\\').slice(0, -1).join('\\')
                        let starts_with_plugin_dir = VALID.plugins.dirs.indexOf(path_without_last_part)!== -1
                        if (starts_with_plugin_dir) {
                            if (OUT.d.args.verbose) console.info(OUT.d.LP, 'plugin_has_valid_path: ', starts_with_plugin_dir, 'path: ', o.p.full_path_to_check)
                            o.f.plugin_has_valid_path = starts_with_plugin_dir
                        } else {
                            if (OUT.d.args.verbose) console.info(OUT.d.LP, 'plugin_has_valid_path no match: ', starts_with_plugin_dir,  'path: ', o.p.full_path_to_check)
                        }
                    //}))
                    o.f.plugin_has_valid_extension = (
                        VALID.plugins.ext.some(ext => o.p.current_path_suffix.endsWith('.' + ext))
                    )
                    o.f.is_plugin =
                        (o.f.plugin_has_drive_root) &&
                        o.f.plugin_has_valid_path && // Check full path
                        o.f.plugin_has_valid_extension // Check suffix for extension
                }
                
                // set by::
                o.f.is_sample =
                // Check captured drive
                    (o.f.sample_has_drive_root || o.f.sample_has_other_root) &&
                    o.f.sample_has_valid_extension // Check suffix for extension
            
                if (o.f.is_plugin && o.f.is_sample) {
                    if (OUT.d.args.debug) console.error(OUT.d.LP, 'process_line: decided is both plugin and sample', o.p.path)
                    OUT.log.push(`decided is both plugin and sample: ${line_out.d.path}`)
                    throw new Error('decided is both plugin and sample')
                    process.exit(1)
                }

                // check if it exists:-
                if (OUT.d.args.plugins && o.f.is_plugin) {
                    try {
                        await fs.access(o.p.path)
                        o.f.exists = true
                        /// if its not already in the array
                        // if (!OUT.d.existing.includes(o.p.path)) {
                        //     OUT.d.log.push(`found plugin: ${o.p.path}`)
                        //     OUT.d.existing.push(o.p.path)
                        //     OUT.log.push(`decided is path: ${line}`) // not needed as non_identified does the 
                        // }
                        // else {
                        //     if (OUT.d.args.debug) console.info(OUT.d.LP,'process_line: plugin already added', o.p.path)
                        // }
                    } 
                    catch (err) {
                        o.f.exists = false
                        if (!o.f.exists) {
                            if (OUT.d.args.verbose) console.info(OUT.d.LP,'process_line: missing', o.p.path)
                            OUT.log.push(`missing: ${o.p.path}`)
                            // if (!OUT.d.missing.includes(o.p.path)) {
                            //     OUT.d.missing.push(o.p.path)
                            // }
                            // else {
                            //     if (OUT.d.args.verbose) console.info(OUT.d.LP,'process_line: already missing', o.p.path)
                            // }
                        }
                    }
                }
                
                // if not and its a sample try it with a different extension
                if (o.f.is_sample && !o.f.exists) {
                    const path_without_ext = nodePath.resolve(nodePath.normalize(o.p.path
                        .split('.')
                        .slice(0, -1)
                        .join('.')))

                    for await (const ext of VALID.samples.ext) {
                        const path_with_ext = path_without_ext + '.' + ext
                        const drive_root_part = [...o.p.path
                            .matchAll(VALID.RE_DRIVE)][0].groups.drive
                        const splitted_path = o.p.path
                            .split('\\')

                        let path_existing = ''
                        let path_missing = ''
                        
                        for (let i=0; i<splitted_path.length; i++) {
                            const path_segment = drive_root_part+':'+'\\'+ 
                                splitted_path
                                    .slice(0, i+1)
                                    .join('\\')
                            try {
                                await fs.access(path_segment)
                                path_existing += path_segment
                            } 
                            catch (err) {
                                path_missing += path_segment
                            }
                        }

                        if (path_existing) {
                            if (OUT.d.args.verbose) console.info(OUT.d.LP,'process_path: found with different extension', path_with_ext)
                            OUT.log.push(`found with different extension: ${path_with_ext}`)
                            o.f.exists = true
                            /// add if it is not already in the array
                            // if (!OUT.d.existing.includes(path_with_ext)) {
                            //     OUT.d.existing.push(path_with_ext)  
                            // }
                            OUT.log.push(`decided is path: ${line}`) // not needed as non_identified does the 
                        } 
                        else {
                            o.f.exists = false
                            //OUT.d.missing.push(o.p.path)
                        }
                    }
                }

                // if the file is still missing span fd to try and find it 
                // if (!o.f.exists) {

                //     // get the basename of the file
                //     const basename = nodePath.basename(o.p.path)
                    
                //     /// for each VALID.samples.drive_root
                //     for await (const drive of VALID.samples.drive_root) {
                //         console.info(OUT.d.LP,'process_path: trying to find sample on another drive root', basename)
                //         OUT.log.push(`trying to find sample on another drive root: ${basename}`)
                //         /// try to find it in these paths:
                //         //VALID.samples.sample_stores
                        
                //         for await (const sample_store of VALID.samples.sample_stores) {
                //             const path_to_check = drive+':\\'+sample_store+'\\'+basename
                //             try {
                //                 await fs.access(path_to_check)
                //                 o.f.exists = true
                //                 /// if its not already in the array
                //                 if (!OUT.d.existing.includes(path_to_check)) {
                //                     OUT.d.log.push(`found sample: ${path_to_check}`)
                //                     OUT.d.existing.push(path_to_check)
                //                 }
                //                 else {
                //                     console.info(OUT.d.LP,'process_path: already exists', path_to_check)
                //                 }
                //                 break
                //             } 
                //             catch (err) {
                //                 continue
                //             }
                //             if (o.f.exists) break
                //             else continue
                //         }

                //     }

                //     /// try the last 2 path bits
                //     //await execa`${OUT.p.FD_COMMAND} ${basename}`
                // }
                // // console.info(OUT.d.LP,'process_path: still missing', o.p.path)
                // OUT.log.push(`still missing: ${o.p.path}`)
                // if (!OUT.d.missing.includes(o.p.path)) {
                //     OUT.d.missing.push(o.p.path)
                // }
                // else {
                //     console.info(OUT.d.LP,'process_path: already missing', o.p.path)
                // }

                OUT.d.processed.push(o)
                /// now make processed unique
                // OUT.d.processed = OUT.d.processed
                //     .sort()
                //     // make the series unique
                //     .reduce(
                //         (acc, current) => {
                //             if (acc.length === 0 || acc[acc.length - 1] !== current) {
                //                 acc.push(current)
                //             }
                //             return acc
                //         },
                //         []
                //     )
                // /// now make exisitn gunique 
                // OUT.d.existing = OUT.d.existing
                //     .sort()
                //     // make the series unique
                //     .reduce(
                //         (acc, current) => {
                //             if (acc.length === 0 || acc[acc.length - 1] !== current) {
                //                 acc.push(current)
                //             }
                //             return acc
                //         },
                //         []
                //     )
                // /// now make missing unique 
                // OUT.d.missing = OUT.d.missing
                //     .sort()
                //     // make the series unique
                //     .reduce(
                //         (acc, current) => {
                //             if (acc.length === 0 || acc[acc.length - 1] !== current) {
                //                 acc.push(current)
                //             }
                //             return acc
                //         },
                //         []
                //     )
            })
        }

        const process_line_took = performance.now() - process_line_started
        console.info(OUT.d.LP, 'process_line: took: ', process_line_took, 'ms')
    }
    // make full_path unique
    line_out.d.full_path = line_out.d.full_path
        .sort()
        // make the series unique
        .reduce(
            (acc, current) => {
                if (acc.length === 0 || acc[acc.length - 1] !== current) {
                    acc.push(current)
                }
                return acc
            },
            []
        )
    return line_out
}


async function process_path(paths) {
    const process_path_started = performance.now()
    OUT.d.ELU.push({name:'process_path',elu:eventLoopUtilization(),now: Date.now()})
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_path: ELU: ', show_last_elu())
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_path: MEM: ', show_memory_usage())
    OUT.d.MEM.push(process.memoryUsage().rss)

    for await(let line_candidate of paths.d.full_path) {
        if (line_candidate === null) {
            throw new Error('processing a line that is null and not valid')
        }

        line_candidate = nodePath.resolve(nodePath.normalize(line_candidate))
        
        //const LOG_STR = `D: ${Date.now()} FROM: ${FROM_FULL_PATH} TO: ${TO_full_path}`
        // OUT.samples
            // 7. for each file found in another location it should symlink that to %asset immediate ... 
            // create symlinks

            if (OUT.d.args.verbose) console.info(OUT.d.LP,'process_path: ', line_candidate)
            OUT.log.push(`processing path: ${line_candidate}`)

            /// create a link inside :- OUT.STATIC_PATHS.MOVED_FILE_LINKS_SYMLINK_DIR

            /// check it exists one file time
            try {
                await fs.access(line_candidate)
                if(OUT.d.args.verbose) console.info(OUT.d.LP,'process_path: exists', line_candidate)
                OUT.log.push(`exists: ${line_candidate}`)
                /// for the last part teh basename make a symlink 
                const symlink_name = nodePath.basename(line_candidate)
                const symlink_path = OUT.STATIC_PATHS.MOVED_FILE_LINKS_SYMLINK_DIR+nodePath.sep+symlink_name
                await link_path(line_candidate, symlink_path)

                /// if the args.copyFiles
                if (OUT.d.args.copyFiles) {
                    const copy_path = OUT.STATIC_PATHS.COPIED_FILE_STORE_DIR+nodePath.sep+symlink_name
                    try {
                        await fs.copyFile(line_candidate, copy_path)
                    } 
                    catch (err) {
                        if (OUT.d.args.verbose) console.error(OUT.d.LP,'process_path: failed to copy file', line_candidate, 'to', copy_path)
                        OUT.log.push(`failed to copy file: ${line_candidate} to ${copy_path}`)
                    }
                }
            } 
            catch (err) {
                if (OUT.d.args.verbose) console.info(OUT.d.LP,'process_path: does not exist', line_candidate)
                OUT.log.push(`does not exist: ${line_candidate}`)
            }
            const process_path_took = performance.now() - process_path_started
            if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_path: took: ', process_path_took, 'ms')
    }
    return Promise.resolve()
}

async function process_strings2_output(p) {
    OUT.d.ELU.push({name: 'process_strings2_output', elu:eventLoopUtilization(),now: Date.now()})
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_strings2_output: ELU: ', show_last_elu())
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_strings2_output: MEM: ', show_memory_usage())
    OUT.d.MEM.push(process.memoryUsage().rss)
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_strings2_output: ', p)
    let counter = 0
    // use ora
    //for await (const line of execa({lines: true})`${normalize_path(OUT.p.STRINGS2_CMD_WITH_ARGS)}`) {
    try {
        //for await (line of execa({stdout: ['pipe', 'inherit']})`${OUT.p.STRINGS2_CMD_WITH_ARGS}`) { 
        OUT.p.RAW_OUT = `${OUT.STATIC_PATHS.I_PROC_VAR_D_FLRUN_D_STR_RAW}${nodePath.sep}${nodePath.basename(p)}.txt`
        /// make teh dir if not existing
        try {
            await fs.access(OUT.STATIC_PATHS.I_PROC_VAR_D_FLRUN_D_STR_RAW)
        } 
        catch (err) {
            if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_strings2_output: creating dir: ', OUT.STATIC_PATHS.I_PROC_VAR_D_FLRUN_D_STR_RAW)
            OUT.log.push(`created dir: ${OUT.STATIC_PATHS.I_PROC_VAR_D_FLRUN_D_STR_RAW}`)
            await fs.mkdir(OUT.STATIC_PATHS.I_PROC_VAR_D_FLRUN_D_STR_RAW, { recursive: true })
        }

        /// touch the file
        await fs.writeFile(OUT.p.RAW_OUT, '')
        // await pipeline(
        //     createReadStream(p),
        //     execa({lines: true})`${OUT.p.STRINGS2_COMMAND.replace('"','')}`.duplex(),
        //     createWriteStream(`${OUT.p.RAW_OUT}`),
        // )
        console.info(OUT.d.LP, 'process_strings2_output: pipeline complete')
        wait(1000)
        console.info(OUT.d.LP, 'process_strings2_output: waiting 1 second for file to be written')
        await new Promise(async (resolve, reject) => {
            await pipeline(
                createReadStream(p),
                execa({lines: true})`${OUT.p.STRINGS2_COMMAND.replace('"','')}`.duplex(),
                createWriteStream(`${OUT.p.RAW_OUT}`)
            )
            createReadStream(OUT.p.RAW_OUT)
            .on('data',(async (line) => {
                    OUT.d.ELU.push({name: 'process_strings2_output: data', elu:eventLoopUtilization(),now: Date.now()})
                    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_strings2_output: data: ELU: ', show_last_elu())
                    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_strings2_output: data: MEM: ', show_memory_usage())
                    OUT.d.MEM.push(process.memoryUsage().rss)
                    // show stream bytes processed
                    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_strings2_output: data: ', line.length, 'bytes')
                    const str_line = line.toString('utf8')
                    counter++
                    if (counter % 100 === 0) {
                        if (OUT.d.args.verbose) console.info(OUT.d.LP,'process_strings2_output: ', counter, ' lines processed')
                    }
                    const processing_result = await process_line(str_line, counter)
                    // its worth continue, now extract the line
                    // in the same cycle
                    /*await *///(async () => { // don't await allow a tick
                        OUT.d.lines.push(processing_result) // blocking - make a copy of the result for later analysis
                        if(processing_result.f.has_path) {
                            await process_path(processing_result)
                        }
                    //})()
                    return processing_result
                }))
                .on('end', () => {
                    console.info(OUT.d.LP,'process_strings2_output: ', counter, ' lines processed')
                    // warn if the data was empty
                    if (counter === 0) {
                        console.warn(OUT.d.LP,'process_strings2_output: no lines processed')
                        OUT.log.push('no lines processed')
                    }
                    resolve()
                })
                .on('error', (err) => {
                    console.error(OUT.d.LP, 'process_strings2_output: ERROR', err)
                    OUT.log.push(`process_strings2_output: ${err}`)
                    reject(err)
                })
                .on('ready', () => {
                    if (OUT.d.args.verbose) console.info(OUT.d.LP,'process_strings2_output: ready')
                })
                .on('close', () => {
                    if (OUT.d.args.verbose) console.info(OUT.d.LP,'process_strings2_output: close')
                    /// if there are results
                    /// if there are missing plugins refuse to start FL Studio
                    /// if there are missing samples or moved they should now be fixed after a brief search
                    OUT.d.ELU.push({name: 'show_processing_results', elu:eventLoopUtilization(),now: Date.now()})
                    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'show_processing_results: ELU: ', show_last_elu())
                    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'show_processing_results: MEM: ', show_memory_usage())
                    OUT.d.MEM.push(process.memoryUsage().rss)
                    if (OUT.d.args.verbose) console.log('')
                    if (OUT.d.args.verbose) console.log('existing: ')
                    /// truncate each to the last 90 chars
                    const existing_truncated = OUT.d.existing.map(e => e.length > 90 ? e.slice(0, 90) + '...' : e)
                    if (OUT.d.args.verbose) console.table(existing_truncated)
                    if (OUT.d.args.verbose) console.log('')
                    if (OUT.d.args.verbose) console.log('missing: ')
                    const missing_truncated = OUT.d.missing.map(m => m.length > 90 ? m.slice(0, 90) + '...' : m)
                    if (OUT.d.args.verbose) console.table(missing_truncated)
                    if (OUT.d.args.verbose) console.log('')
                    if (OUT.d.args.verbose) console.log('non identified: ')
                    const non_identified_truncated = OUT.d.non_identified.map(n => n.length > 90 ? n.slice(0, 90) + '...' : n)
                    if (OUT.d.args.verbose) console.table(non_identified_truncated)
                    if (OUT.d.args.verbose) console.log('')
                    setTimeout(resolve, 1000)
                })
                .on('finish', () => {
                    if (OUT.d.args.verbose) console.info(OUT.d.LP,'process_strings2_output: finish')
                })
                .on('pending', () => {
                    if (OUT.d.args.verbose) console.info(OUT.d.LP,'process_strings2_output: pending')
                })
            })
                .then(() => {
                    if (OUT.d.args.verbose) console.info(OUT.d.LP,'process_strings2_output: then')
                })
                .finally(() => {
                    if (OUT.d.args.verbose) console.info(OUT.d.LP,'process_strings2_output: finally')
                })
            
    }
    catch (err) {
        console.error(OUT.d.LP, 'process_strings2_output: ', err)
        OUT.log.push(`process_strings2_output: ${err}`)
    }
    if (OUT.d.args.verbose) console.info(OUT.d.LP,'process_strings2_output: ', counter, ' lines processed')
    return Promise.resolve()
}

// starts above logic and processes the flp to have symlinks to samples and plugins, the hash, and the log
async function process_flp(p) {
    const process_flp_started = performance.now()
    OUT.d.ELU.push({name: 'process_flp', elu:eventLoopUtilization(),now: Date.now()})
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_flp: ELU: ', show_last_elu())
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'process_flp: MEM: ', show_memory_usage())
    OUT.d.MEM.push(process.memoryUsage().rss)
    try {
        if (OUT.d.args.verbose) console.info(OUT.d.LP,'process_flp: ', p)
        OUT.log.push(`processing flp: ${p}`)
        await create_hash_file(p)
        await process_strings2_output(p)
    } 
    catch (err) {
        console.error(OUT.d.LP, 'process_flp: ', err)
        OUT.log.push(`process_flp: ${err}`)
    }
    const process_flp_took = performance.now() - process_flp_started
    console.info(OUT.d.LP, 'process_flp: took: ', process_flp_took, 'ms')
    return Promise.resolve()
}


// 8. start FL
async function start_fl() {
    OUT.d.ELU.push({name: 'start_fl', elu:eventLoopUtilization(),now: Date.now()})
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'start_fl: ELU: ', show_last_elu())
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'start_fl: MEM: ', show_memory_usage())
    OUT.d.MEM.push(process.memoryUsage().rss)
    const LOG_STR = `D: ${Date.now()} FLBIN_PATH: ${OUT.STATIC_PATHS.FLBIN_PATH} SRC_FLP: ${OUT.p.SRC_FLP}`
    console.info(OUT.d.LP, 'START_FL: ', LOG_STR)
    OUT.log.push(LOG_STR)
    
    /// first get a dir_hash of fl studio folder
    OUT.md.fl_studio_hash = await dir_hash(OUT.STATIC_PATHS.FL_PATH)
    console.info(OUT.d.LP, 'fl_studio_hash: ', OUT.md.fl_studio_hash)
    OUT.log.push(`fl_studio_hash: ${OUT.md.fl_studio_hash}`)

    // TODO: only tar if it changed

    const TAR_ARCHIVE = 'E:\\b\\arc\\fl-run\\d\\24.2.2.4597-bin.tar'

    /// if ram mode 
    if (OUT.d.args.ram) {
        console.info(OUT.d.LP, 'ram mode enabled')
        //OUT.STATIC_PATHS.RAM_DRIVE_FL_STUDIO
        // if the tar does not exist create one
        try {
            console.info(OUT.d.LP, 'checking for fl_studio tar: ', TAR_ARCHIVE)
            await fs.access(TAR_ARCHIVE)
            // if its a link process.exit(1)
            if (await isLink(TAR_ARCHIVE)) {
                console.error(OUT.d.LP, 'fl_studio tar is a link, bailing...')
                OUT.log.push('fl_studio tar is a link, bailing...')
                process.exit(1)
            }
            if(!isTar(TAR_ARCHIVE)) {
                /// remove teh previous failure try both
                    // if its directory remove it
                    // if its a file remove it
                    try {
                        //await fs.rm(TAR_ARCHIVE, { recursive: true, force: true })
                        // instead of dangerous .rm rename instead
                        await fs.rename(TAR_ARCHIVE, TAR_ARCHIVE+'-old')
                    }
                    catch(err) {
                        throw err
                    }
            }
        }
        catch (err) {
            console.info(OUT.d.LP, 'fl_studio tar not found, creating...')
            OUT.log.push('fl_studio tar not found, creating...')
            //await tar(OUT.STATIC_PATHS.FL_PATH, OUT.STATIC_PATHS.ARC_FL_STUDIO)
            if (!fs.access(TAR_ARCHIVE)) {
                await new Promise((resolve, reject) => {
                    tar.c({
                        // 'z' is alias for 'gzip' option
                        z: true,

                    },
                    [OUT.STATIC_PATHS.FL_PATH]
                    )
                        .pipe(createWriteStream(TAR_ARCHIVE))
                        .on('error', (err) => {
                            console.error(OUT.d.LP, 'fl_studio tar creation error: ', err)
                            OUT.log.push(`fl_studio tar creation error: ${err}`)
                            reject(err)
                        })
                        .on('finish', () => {
                            console.info(OUT.d.LP, 'fl_studio tar created')
                            OUT.log.push('fl_studio tar created')
                            resolve()
                        })

                    })
                }
        }
        // check the previous operation worked
        try {
            await fs.access(TAR_ARCHIVE)
        }
        catch (err) {
            if (OUT.d.args.verbose) console.error(OUT.d.LP, 'fl_studio tar not found after creation, bailing...')
            OUT.log.push('fl_studio tar not found after creation, bailing...')
            process.exit(1)
        }

        // expand the tar to the ram drive
        tar.x({f:TAR_ARCHIVE,cwd: OUT.STATIC_PATHS.RAM_DRIVE_FL_STUDIO})
        console.info(OUT.d.LP, 'fl_studio tar expanded to ram drive')
        OUT.log.push('fl_studio tar expanded to ram drive')
        OUT.d.ELU.push({name: 'start_fl-tar_extract', elu:eventLoopUtilization(),now: Date.now()})

        // show the results with a readdir
        const ram_dir_entries = await fs.readdir(OUT.STATIC_PATHS.RAM_DRIVE_FL_STUDIO)
        for (const entry of ram_dir_entries) {
            console.info(OUT.d.LP, 'ram_dir_entries: ', entry)
        }
    }

    const ram_dir_entries = await fs.readdir(OUT.STATIC_PATHS.RAM_DRIVE_FL_STUDIO)
    for (const entry of ram_dir_entries) {
        console.info(OUT.d.LP, 'ram_dir_entries: ', entry)
    }

    console.info('!!!!!! STARTING FL STUDIO !!!!!!!!!!')
    //await execa(OUT.STATIC_PATHS.FLBIN_PATH, [OUT.p.SRC_FLP])
	// 9. FL ended now make a hash of the flp and store it for next time.
}

export async function main() {
    OUT.p.STRINGS2_CMD_WITH_ARGS = `${OUT.p.STRINGS2_COMMAND.replace('\\\\','\\')} "${OUT.p.SRC_FLP}"`
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'STRINGS2_CMD_WITH_ARGS: ', OUT.p.STRINGS2_CMD_WITH_ARGS)


    console.log('')
    console.info('..:: FL RUN ::..')
    console.info('----------------')
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'env_path: ', OUT.p.env_path)
    
    // 2. check if the src flp has been run before by checking for existing hash
    try {
        await fs.access(OUT.STATIC_PATHS.I_PROC_VAR_D_FLRUN_D_HASH)
    } 
    catch (err) {
        if (OUT.d.args.verbose) console.info(OUT.d.LP, 'hash dir not found, creating...')
        OUT.log.push('hash dir not found, creating...')
        await fs.mkdir(OUT.STATIC_PATHS.I_PROC_VAR_D_FLRUN_D_HASH, { recursive: true })
    }
    OUT.md.number_of_hashes = (
        await fs.readdir(OUT.STATIC_PATHS.I_PROC_VAR_D_FLRUN_D_HASH)
    ).length
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'number_of_hashes: ', OUT.md.number_of_hashes)
    OUT.log.push(`number of hashes: ${OUT.md.number_of_hashes}`)

    OUT.md.src_flp_hash = await get_hash_file(OUT.p.SRC_FLP)

    console.info(OUT.d.LP, '::::STARTING:::', Date.now())

    // 2.1 if the file didnt changed since this was last run then it bails
    OUT.p.FLP_HASH_PATH = OUT.STATIC_PATHS.I_PROC_VAR_D_FLRUN_D_HASH+'\\'+nodePath.basename(OUT.p.SRC_FLP)
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'FLP_HASH_PATH: ', OUT.p.FLP_HASH_PATH)
    try {
        OUT.f.has_hash = await has_hash(OUT.p.SRC_FLP)
        if (OUT.f.has_hash) {
            OUT.md.existing_hash = await fs.readFile(OUT.p.FLP_HASH_PATH, 'utf8')
            if (OUT.md.existing_hash === OUT.md.src_flp_hash) {
                if (OUT.d.args.verbose) console.info(OUT.d.LP, 'file has not changed since last run, bailing...')
                OUT.log.push('file has not changed since last run, bailing...')
                // skips processing flp
            }
        }
        else {
            if (OUT.d.args.verbose) console.info(OUT.d.LP, 'no existing hash found, processing...')
            OUT.log.push('no existing hash found, processing...')
            console.log('')
            await process_flp(OUT.p.SRC_FLP)
            console.log('')
        }
    } 
    catch (err) {
        throw err
    }
    await start_fl(OUT.p.SRC_FLP)
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'exit: ELU: ', show_last_elu())
    OUT.d.ELU.push({name: 'exit', elu:eventLoopUtilization(),now: Date.now()})
    if (OUT.d.args.verbose) console.info(OUT.d.LP, 'exit: MEM: ', show_memory_usage())
    OUT.d.MEM.push(process.memoryUsage().rss)
    OUT.d.ended = performance.now()
    OUT.d.took = OUT.d.ended - OUT.d.started
    console.info(OUT.d.LP, 'took: ', OUT.d.took, 'ms')
    OUT.d.MEM.push(process.memoryUsage().rss)
    /// make sure the file is nested and human readable
    const job_data = JSON.stringify(OUT, null, 2)
    //console.log(job_data)
    assert(job_data.length > 0, 'job_data is empty')
    try {
        const path_job_data = OUT.STATIC_PATHS.I_PROC_VAR_D_FLRUN_JOB_DATA+nodePath.sep+`${nodePath.basename(OUT.p.SRC_FLP)}-${Date.now()}.json`
        if (OUT.d.args.verbose) console.info(OUT.d.LP, 'write_job_data: ', path_job_data)
        OUT.log.push(`wrote job data: ${path_job_data}`)
        await fs.writeFile(path_job_data, Buffer.from(job_data.toString('utf8')))
    }
    catch (err) {
        console.error(OUT.d.LP, 'write_job_data: ', err)
        OUT.log.push(`write_job_data: ${err}`)
    }
}

if(nodePath.basename(fileURLToPath(import.meta.url)) === 'fl-run.mjs') {
    await main().catch(err => {
        console.error('Error:', err)
        process.exit(1)
    })
}