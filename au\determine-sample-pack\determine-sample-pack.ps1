# Script: determine-sample-pack.ps1
# Description: Collates metadata about a sample pack and outputs a JSON file in the root directory.

param (
    [string]$SamplePackPath = "."
    [string]$recurse = $False
)

# Function to determine file types in the directory
function Get-FileTypes {
    param (
        [string]$Path
    )
    $files = Get-ChildItem -Path $Path -Recurse -File
    $types = $files | ForEach-Object { $_.Extension.ToLower() } | Sort-Object -Unique
    return $types
}

# Function to test if the directory contains non-audio files
function Test-IsMultiKit {
    param (
        [array]$FileTypes
    )
    $nonAudioExtensions = @(".mid", ".fst", ".flp")
    return ($FileTypes | Where-Object { $nonAudioExtensions -contains $_ }).Count -gt 0
}

# Main logic
if (-Not (Test-Path $SamplePackPath)) {
    Write-Error "The specified path does not exist."
    exit 1
}

$metadata = @{
    types = Get-FileTypes -Path $SamplePackPath
    flags = @{
    }
}
$metadata.flags.is_mk = Test-IsMultiKit -FileTypes $metadata.types

# Output metadata to JSON file
$jsonOutput = $metadata | ConvertTo-Json -Depth 10 -Compress
$outputFilePath = Join-Path -Path $SamplePackPath -ChildPath "metadata.json"
Set-Content -Path $outputFilePath -Value $jsonOutput -Encoding UTF8

Write-Host "Metadata has been written to $outputFilePath"