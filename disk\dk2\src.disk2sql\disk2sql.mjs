#!/usr/bin/env node

import * as fs from 'node:fs/promises'
import * as path from 'node:path'
import { parseArgs } from 'node:util'
import * as sqlite3 from 'sqlite3'
import * as git from 'simple-git/promise'
import chalk from 'chalk'
import { performance } from 'node:perf_hooks'

const startTime = performance.now()
process.on('exit', () => {
    const endTime = performance.now()
    const duration = endTime - startTime
    console.log(chalk.green(`Script execution time: ${duration.toFixed(2)} milliseconds`))
})

const DATABASE_VERSION = '1.0'
const ARG_DEF = {
    verbose: { type: 'boolean', short: 'v' },
    // TODO: (matt): is this case insensitive?
    force: { type: 'boolean', short: 'F' },
    from: { type: 'string', short: 'fr' },
    to: { type: 'string' },
    force: { type: 'boolean', short: 'F' },
    microsleep: { type: 'int', short: 'ms' },
    help: { type: 'boolean', short: 'h' }
}
const { values: args } = parseArgs(ARG_DEF)
// TODO: (matt): what type is returned if it fails?
if (!args) console.error(chalk.red(`Problem parsing args`)) && process.exit(1)

const PATHS = {
    cwd: process.cwd(),
    from: args.from || process.cwd(),
    to: args.to || process.cwd()
}

function sanitizeDbNameString(toPath) {
    return path
        .resolve(toPath)
        .replace(/[^a-zA-Z0-9]/g, '_')
        .concat('.db')
}

const sleep = async (microsleep) => {
    if (microsleep > 0)
        await new Promise((resolve) => setTimeout(resolve, microsleep / 1000))
}

async function getFileMetadata(filePath) {
    const stats = await fs.stat(filePath)
    return {
        path: filePath,
        size: stats.size,
        isDirectory: stats.isDirectory(),
        createdAt: stats.birthtimeMs,
        modifiedAt: stats.mtimeMs,
        accessedAt: stats.atimeMs,
        mode: stats.mode,
        uid: stats.uid,
        gid: stats.gid
    }
}

async function recurseDirectory(dirPath) {
    try {
        if (args.verbose) console.log(chalk.cyan(`Processing directory: ${dirPath}`))

        const entries = await fs.readdir(dirPath, { withFileTypes: true })

        for await (const entry of entries) {
            const fullPath = path.join(dirPath, entry.name)
            if (!entry.isDirectory()) {
                const metadata = await getFileMetadata(fullPath)
                stmt.run(
                    metadata.path,
                    metadata.size,
                    metadata.isDirectory ? 1 : 0,
                    metadata.createdAt,
                    metadata.modifiedAt,
                    metadata.accessedAt,
                    metadata.mode,
                    metadata.uid,
                    metadata.gid
                )
            }
            if (args.verbose) console.log(chalk.blue(`Processed file: ${fullPath}`))
            if (args.microsleep) await sleep(args.microsleep)
        }
    }
    catch (error) {
        console.error(chalk.red(`Error processing ${dirPath}:`, error))
    }
}

let stmt

;(async () => { try {

    console.log(`The SQLite Db is versioned in git by default`)

    PATHS.db = path.join(PATHS.to, sanitizeDbName(PATHS.to))
    PATHS.dbParent = path.dirname(PATHS.db)

    const gitRepo = git(PATHS.to)
    const isRepo = await gitRepo.checkIsRepo()

    if (!isRepo) {
        console.log(chalk.yellow('Initializing Git repository...'))
        await gitRepo.init()
        console.log(chalk.green('Git repository initialized.'))
    }
    else {
        console.log(chalk.yellow('Found existing git'))
    }

    try {
        if (await fs.access(PATHS.db).then(() => true).catch(() => false)) {
            if (args.force) {
                console.log(chalk.yellow(`Database already exists at ${PATHS.db}. Removing...`))
                await fs.rm(PATHS.db)
            }
            else {
                console.error(
                    chalk.red(`Error: Database already exists at ${PATHS.db}. Use --force to overwrite.`)
                )
                process.exit(1)
            }
        }
    }
    catch (err) {
        console.error(chalk.red(`Error: checking for database: ${err.message}`))
        process.exit(1)
    }

    console.log(chalk.green(`Create path if missing: ${PATHS.dbParent}`))
    await fs.mkdir(PATHS.dbParent, { recursive: true })

    const db = new sqlite3.Database(PATHS.db, (err) => {
        if (!err) {
            console.log(chalk.green(`Connected to SQLite database at ${PATHS.db}`))
        }
        else {
            console.error(chalk.red(`Error: with new SQLite3 db read: ${err.message}`))
            process.exit(1)
        }
    })

    db.serialize(() => {
        console.info(`Creating db at version ${DATABASE_VERSION}`)

        db.run(`CREATE TABLE IF NOT EXISTS metadata (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT UNIQUE,
            value TEXT
        )`)

        db.run(`CREATE TABLE IF NOT EXISTS files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            path TEXT,
            size INTEGER,
            isDirectory INTEGER,
            createdAt INTEGER,
            modifiedAt INTEGER,
            accessedAt INTEGER,
            mode INTEGER,
            uid INTEGER,
            gid INTEGER
        )`)

        db.run(
            `INSERT INTO metadata (key, value)
            VALUES ('version', ?)
            ON CONFLICT(key) DO UPDATE SET value = excluded.value`,
            [DATABASE_VERSION]
        )
    })

    stmt = db.prepare(
        'INSERT INTO files (path, size, isDirectory, createdAt, modifiedAt, accessedAt, mode, uid, gid) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)'
    )

    // console.log(chalk.cyan(`Indexing directory: ${PATHS.from}`))
    // await recurseDirectory(PATHS.from)
    // console.log(chalk.green('Indexing completed.'))

    // ;(async () => {
    //     const row = await db.get('SELECT COUNT(*) AS file_count FROM files')
    //     console.log(`Total number of files indexed: ${row.file_count}`)
    // })()

    // // commit changes
    // try {
    //     await gitRepo.add(PATHS.db)

    //     const message = `Add SQLite database with file metadata from ${PATHS.from}`
    //     await gitRepo.commit(message)

    //     await gitRepo.push('origin', 'main')
    //     console.log(chalk.green('Changes committed and pushed to Git.'))
    // }
    // catch (error) {
    //     console.error(chalk.red('Error committing changes to Git:', error))
    // }
}
catch (error) {
    console.error()
    console.error(chalk.red('Error:', error))
    console.error()
}
finally {
    stmt.finalize()
    db.close((err) => {
        if (err) console.error(chalk.red(err.message))
        else console.log(chalk.green('Database connection closed.'))
    })
}
})()
