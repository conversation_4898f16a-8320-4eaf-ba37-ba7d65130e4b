LOADBTM ON
BREAK ON
REM have to use quotation marks with set and type

IF "%1" == "" GOTO END

IF "%1" == "/?" GOTO HELP
IF "%1" == "/H" GOTO HELP
IF "%1" == "/h" GOTO HELP

IF "%2" == "/D" GOSUB DIRS
IF "%2" == "/S" GOSUB FILESELCT

IF "%1" == "/U" GOTO UPPER
IF "%1" == "/L" GOTO LOWER



:LOWER
set newname="%@lower[%1]"
ren %1 %newname%
GOTO END
REM condition when end of file list is met

:UPPER
set newname="%@upper[%1]"
ren %1 %newname%
REM condition when end of file list is met

:DIRS
RETURN

:HELP
ECHO use: chcase /l filename
ECHO chcase /u filename
ECHO.
:END
QUIT
