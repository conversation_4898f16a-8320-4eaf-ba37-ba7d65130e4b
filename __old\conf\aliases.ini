..          cd ..
...         cd ...
....        cd ....
.....       cd .....
cd          cdd
m           memory
df          free e: && free i: && free m: && free r: && free w:
rdb         del i:\script\conf\jpstree.idx && cdd /u
ren 	    *rename

rmf         *del /t /z /w
rmd         rmdir /s

a           type %conf%\aliases.ini
k           type %conf%\keycuts.ini
b           type %conf%\bin.ini
ea          mp i:\script\conf\aliases.ini
ek          mp i:\script\conf\keycuts.ini
eb          mp i:\script\conf\bin.ini

rap	    net stop apache2 && net start apache2
ua	    CALL uapache.btm %1
dev	    CALL setdevenv.cmd

time        echo %time%
ddirs       `describe /a:d *.*`

fsck        chkdsk /f
calc        `echo result: %@eval[%1 %2 %3 %4 %5 %6 %7 %8 %9]`

gam         cdd d:\games
shmem       "i:\org\short_memory.txt"

zipview     unzip -l %1 %2 %3 %4 %5 %6 %7 %8 %9

idea        cdd e:\documents and settings\mbishop\ideaProjects