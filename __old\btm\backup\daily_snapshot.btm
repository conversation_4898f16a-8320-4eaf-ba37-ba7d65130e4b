REM make a "snapshot" each day of the week

REM IF "%1"=="" GOTO help

ECHO.
ECHO DATE: %_DATE

REM note with 4nt the set assignments must have no spaces next to the equals on
REM both sides
SET dd=%@SUBSTR[%_DATE,0,2]
ECHO ^tdd = %dd%

SET num=%@EVAL[%dd% %% 7]
ECHO ^ttodays number is: %num%

ECHO.

SET projectname=%1
SET snapshot_location=%2
SET target_directory=%3
SET file_mask=%4
REM this is addded to the robocopy command regardless
IF /i "%5"=="/s" (SET subdirs="/s") ELSE (SET sudirs="")

REM *************************************************************************
REM make the projectname if it doesnt exist

IF /i NOT EXIST

REM *************************************************************************
REM robocopy 
REM /s /e copy subdirs and empty ones
REM /purge delete files that no longer exist in source
REM 

REM delete the last date file indicating what date this was
DEL date.txt

REM create a new date file so we know exactly what this date was
ECHO %_DATE > date.txt

:help
ECHO ::HELP::
ECHO ^t^tfor SNAPSHOT by mbishop
ECHO.
ECHO this utility needs robocopy from Microsoft Resource pack
ECHO as it overcomes a deficiency in the Dos with filenames longer
ECHO than 255 characters. It also provides logs and deletion of files
ECHO missing in the source directory.
ECHO.
ECHO inside the snapshot_location each day of the week is numbered
ECHO and for each projectname a dir resides with the snapshot files
ECHO.
ECHO ^tdaily_snapshot.btm
ECHO.
ECHO ^tparameters
ECHO ^t^tprojectname^t^tname of dir in snapshot_location\day
ECHO ^t^tsnapshot_location^tlocation of master snapshot directory
ECHO ^t^ttarget_directory^tthing to backup
ECHO ^t^tfile_mask^t^twildcards...etc...
ECHO ^t^tsubdirs^t^t^tspecified as /s
ECHO.
ECHO ::EG::
ECHO ^tdaily_snapshot.btm i:\__backup\snapshot\byday i:\projects *.*
ECHO ^tdaily_snapshot.btm i:\__backup\snapshot\byday i:\projects\cpp\ *.cpp
ECHO ^tdaily_snapshot.btm i:\__backup\snapshot\byday c:\usr\bin\slsk\ *.cfg
