$INST_LOCATION_DRIVES = @('c','d','e','f','h','i','j','k','p','r','s','u','w','x')

$recent_inst_changes =@()

foreach ($inst_drive_root in $INST_LOCATION_DRIVES) {
    $recent_inst_changes += Get-ChildItem -Path $inst_location_root+'$:/pool/au/inst' -Recurse | Where-Object { 
        $_.LastWriteTime -gt (Get-Date).AddDays(-7) 
    }
}

$recent_inst_changes = $recent_inst_changes | Sort-Object -Property LastWriteTime

$possible_colours = @("Red", "Green", "Blue", "Yellow", "Cyan", "Magenta")

$i = 0
foreach ($file in $recent_inst_changes) {
    $colour = $possible_colours[$i % $possible_colours.Count]

    $i++

    Write-Host $file.FullName -ForegroundColor $colour
}

Write-Host "changes found: " $recent_inst_changes.Length
