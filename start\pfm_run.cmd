@echo off
REM  -w allow everyone read write access
REM  -i skip if mounted


REM mount temporary drive

REM  -u do not create mount point use C:\Volumes
REM pfm mount -i -w P:\b\mnt\pictures-mirror.pfo

REM pfm mount -i -w H:\b\mnt\public-documents-mirror.pfo

REM pfm mount -i -w P:\b\mnt\working-dev-mirror.zip

REM echo
REM pfm list


REM "C:\Program Files\Pismo File Mount Audit Package"
REM "C:\Program Files\Pismo File Mount Audit Package\pfmcontrol.exe"

REM #start explorer to view it 


REM CALL "I:\proc\start\pfm_on_src.cmd"
REM CALL "I:\proc\start\pfm_on_samples_mirror.cmd"