@ECHO OFF
SETLOCAL ENABLEDELAYEDEXPANSION

REM Check if a path argument was provided
IF "%~1"=="" (
    ECHO Usage: %~n0 path\to\check
    ECHO Example: %~n0 Users\Public\Documents
    GOTO :EOF
)

REM Use a different variable name to avoid conflict with system PATH
SET "TARGET_PATH=%~1"

REM Remove trailing backslash if present, unless it's just the root "\"
IF "%TARGET_PATH:~-1%"=="\" (
    IF NOT "%TARGET_PATH%"=="\" (
        SET "TARGET_PATH=%TARGET_PATH:~0,-1%"
    )
)

REM List of drives to check
SET DRIVES=c,d,e,f,h,i,j,k,o,p,m,r,s,t,u,w,x

ECHO Checking for path "%TARGET_PATH%" on specified drives...
ECHO.

REM Loop through each drive letter in the DRIVES variable
FOR %%G IN (%DRIVES%) DO (
    ECHO --- Listing Drive %%G: ---

    REM Construct the full path for the current drive
    SET "FULL_CHECK_PATH=%%G:\%TARGET_PATH%"

    REM Check if the path exists on the current drive
    IF EXIST "!FULL_CHECK_PATH!" (
        ECHO Found on %%G: - Listing contents of "!FULL_CHECK_PATH!"
        DIR "!FULL_CHECK_PATH!"
    ) ELSE (
        ECHO Path "!TARGET_PATH!" does not exist on drive: %%G:
    )
    ECHO.
)

ECHO.

ENDLOCAL
GOTO :EOF