<#
.SYNOPSIS
    A tool to aid the extraction of files from distributions of assets.. Typically used for music related activities.

.DESCRIPTION

.NOTES

.TODO
    1. uses $SESSION to store any changes, so we know paths to remove.
    and this way I can relate everything that happened... lets use $OPERATION
#>

[CmdletBinding(
    DefaultParameterSetName="PROCESS_FILE"
)]
param(

    # source file
    [Parameter(
        Mandatory= $True,
        Position=0
    )]
    [string]$SRC_PATH
    
    ,
    
    # dest path - if empty is the same as src_file
    [Parameter(
        Mandatory= $False,
        Position=1
    )]
    [string]$TARGET_PATH
    
    ,

    # FORCE THE OPERATION - this will make a direct operation.. 
    [Parameter(
        Mandatory= $False
    )]
    [string]$OPERATION
    ,
    
    # persist to appdata a temporary location in config XML
    # manually edit to avoid this on command line
    [Parameter(
        Mandatory= $False
    )]
    [string]$TMP

    # if the directory has another directory with nothing in it,
    # at the first depth, auto remove it ( badly packed and pointless)
    #,
    # [Parameter(
    #     Mandatory = $False
    # )]
    # [string]$FLATTEN_DIRS
)

#region ex-script-defaults
$PSDefaultParameterValues = @{ '*:Encoding' = '$this.d.encoding' }
$PSDefaultParameterValues['Out-File:Encoding'] = '$this.d.encoding'
$ErrorActionPreference = "Stop"
$PSStyle.Progress.View = "Minimal"
#endregion ex-script-defaults
# ---------------------------------------------------------------------------------------
# DEPENDENCIES
#region ex-util

# ---- UTILITY
Function Format-FileSize() {
    Param ([int]$size)
    If ($size -gt 1TB) {[string]::Format("{0:0.00} TB", $size / 1TB)}
    ElseIf ($size -gt 1GB) {[string]::Format("{0:0.00} GB", $size / 1GB)}
    ElseIf ($size -gt 1MB) {[string]::Format("{0:0.00} MB", $size / 1MB)}
    ElseIf ($size -gt 1KB) {[string]::Format("{0:0.00} kB", $size / 1KB)}
    ElseIf ($size -gt 0) {[string]::Format("{0:0.00} B", $size)}
    Else {""}
}

function Get-MemUsage {
    $powerShellProcess = Get-Process -Id $PID
    $memoryUsageBytes = $powerShellProcess.WorkingSet64
    $memoryUsageMB = $memoryUsageBytes / 1MB
    return "$($memoryUsageMB) MB"
}

function Get-CpuUsage {
    return (Get-Counter '\Processor(_Total)\% Processor Time').CounterSamples.CookedValue
}

#endregion ex-util
# ---- MLOG 
#region ex-mlog
class MLOG {
    [string]$PREFIX = "->"
    [hashtable]$log_flags = @{
        quiet = $False
        debug = $False
    }
    MLOG([boolean]$quiet, [string]$prefix) {
        if($quiet) {$this.log_flags.quiet = $quiet}
        if($prefix) {$this.PREFIX = $prefix}
    }
    MLOG([boolean]$quiet) {$this.log_flags.quiet = $quiet}
    setDebug([boolean]$state) {$this.log_flags.debug = $state}
    # used by each log level
    [void]_print([array]$in_colours,[array]$logs) {
        if(-not $this.log_flags.quiet) {
            if ($in_colours.Length -eq 3) {
                $used_colours = @("DarkBlue")+$in_colours
            } else {$used_colours = $in_colours}
            Write-Host $this.PREFIX -NoNewLine -ForegroundColor $used_colours[0];
            Write-Host $logs[0] -NoNewline -ForegroundColor $used_colours[1];
            Write-Host $logs[1] -NoNewline -ForegroundColor $used_colours[2];
            Write-Host $logs[2] -ForegroundColor $used_colours[3];
        }
    }
    # uses default used_colours
    [void]_print([array]$logs) {
        if(-not $this.log_flags.quiet) {
            Write-Host $this.PREFIX -NoNewLine -ForegroundColor DarkGray;
            Write-Host $logs[0] -NoNewline -ForegroundColor DarkGray;
            Write-Host $logs[1] -NoNewline -ForegroundColor Gray;
            Write-Host $logs[2] -ForegroundColor White;
        }
    }
    [void]testing($log) {
        Write-Host $log[0] -ForegroundColor DarkCyan;
    }
    [void]one($log) {if(-not $this.log_flags.quiet) {Write-Host $log[0] -ForegroundColor Gray}}
    [void]o([System.Array]$log) {$this.one($log)}

    [void]br([array]$logs) {if(-not $this.log_flags.quiet) {Write-Host}}
    [void]b([array]$logs) {$this.br()}
    
    # default
    [void]def([array]$logs) {$this._print($logs)}
    [void]d([array]$logs) {$this.def($logs)}
    
    #Black,DarkBlue,DarkGreen,DarkCyan,DarkRed,DarkMagenta,DarkYellow,Gray,DarkGray,Blue,Green,Cyan,Red,Magenta,Yellow,White

    static[array]$ERROR_COLOURS = @("DarkRed","DarkRed","Red")
    [void]error([array]$logs) {$this._print([MLOG]::ERROR_COLOURS,$logs)}
    [void]err([array]$logs) {$this.error($logs)}
    [void]e([array]$logs) {$this.error($logs)}

    [void]errorbr([array]$logs) {Write-Host;$this.error($logs);Write-Host}
    [void]eb([array]$logs) {$this.errorbr($logs)}
    
    static[array]$GOOD_COLOURS = @("DarkGreen","Green","Green")
    [void]good([array]$logs) {$this._print([MLOG]::GOOD_COLOURS,$logs)}
    [void]g([array]$logs) {$this.good($logs)}

    static[array]$HEAD_COLOURS = @("DarkCyan","White","White")
    [void]head([array]$logs) {$this._print([MLOG]::HEAD_COLOURS,$logs)}
    [void]h([array]$logs) {$this.head($logs)}
    
    static[array]$INFO_COLOURS = @("DarkBlue","Blue","Cyan")
    [void]info([array]$logs) {$this._print([MLOG]::INFO_COLOURS,$logs)}
    [void]i([array]$logs) {$this.info($logs)}
    
    static[array]$WARNING_COLOURS = @("DarkYellow","Yellow","Magenta")
    [void]warning([array]$logs) {$this._print([MLOG]::WARNING_COLOURS,$logs)}
    [void]warn([array]$logs) {$this.warning($logs)}
    [void]w([array]$logs) {$this.warning($logs)}
}
$script:L = [MLOG]::new($QUIET)
$L.setDebug($True)
$script:L = [MLOG]::new("EXTRACA//$(Get-CpuUsage)cpu//")
$L.setDebug($True)
#endregion ex-mlog
# ---------------------------------------------------------------------------------------
# ---- ARGS-FLAGS
#region ex-args-flags

#Set-Variable -Name VERBOSE -Value $True -Option Constant
#$PSCmdlet.MyInvocation.BoundParameters["Verbose"].IsPresent
if ($PSCmdlet.MyInvocation.BoundParameters.ContainsKey("Verbose")) {
    #Set-Variable -Name VERBOSE -Value $True -Option Constant
    $script:L.w(@("Local-Scope: ","Verbose mode: ","enabled."))
} else {
    #Set-Variable -Name VERBOSE -Value $False -Option Constant
}

# if ("Continue" -eq $DebugPreference) {
#Set-Variable -Name DEBUG -Value $True -Option Constant
if ($PSCmdlet.MyInvocation.BoundParameters.ContainsKey("Debug")) {
    #Set-Variable -Name DEBUG -Value $True -Option Constant
    $script:L.w(@("Local-Scope: ", "Debugging mode: ", "enabled."))
} else {
    #Set-Variable -Name DEBUG -Value $False -Option Constant
}

#endregion ex-args-flags

# ---------------------------------------------------------------------------------------
# ---------------------------------------------------------------------------------------
# ---- EXTRACA CLASS 
# SESSION CLASS RECORDS THE HISTORY OF THE OPERATOIN
class ExtracaJob {
    ExtracaJob() {
        
    }

    static [hashtable]$data=@{
        file_count = 0
        dir_count = 0
        seen_paths = @{

        }
    }

    [hashtable]$hashes=@{   
    }

    # -- flags
    [hashtable]$arc_flags= @{
        # set the characteristics of the archive
        is_one_file = $False
        is_many_files = $False
        has_one_dir = $False
        has_many_dirs = $False
    }

    [hashtable]$op_flags= @{
        # set that the task is valid and to continue
        is_valid_operation = $False
    }

    [hashtable]$session_flags= @{
        has_run = $False
        completed = $False
    }

    # record a path
    path(
        $path = ""
    ) {

    }
}

#region ex-class
class ExtracaClass {
    #region ex-class-var
    static [string[]]$MANDATORY_DEPS=@(
        "7zS.exe" # ==> "D:\usr\bin\7zS.exe" ==> 7zip Z STANDARD
    )
    static [int]$LIST_DATETIME_TRIM_LENGTH=21
    static [string]$RE_7ZIPZSTD_DATETIME_STR="^.+([0-9]{4})-([0-9]{2})-([0-9]{2})\s{1}([0-9]{2}):([0-9]{2}):([0-9]{2}):([0-9]{2})$"
    static [string]$RE_7ZIPZSTD_ATTR_STR="^.+([FD\.]+)$"
    static [string]$RE_7ZIPZSTD_SIZE_STR="^.+([0-9]+)$"
    static [string]$RE_7ZIPZSTD_COMPRESSED_SIZE_STR="^.+([0-9]+)
    $"

    # it can have no path if its in the root
    # this one is really hard since a file can contain so many different characters
    # we instead def to extract, the previous and assume the end of each line is just the path...
    static [string]$RE_7ZIPZSTD_PATH_STR="^.([a-z0-9\\\.]|\s)$"

    #[ExtracaClass]::RE_7ZIPZSTD_COL_NAMES,

    static [System.Collections.ArrayList]$7ZIPSTD_RE_ARRAY_LIST=@(
        [ExtracaClass]::RE_7ZIPZSTD_DATETIME_STR
        [ExtracaClass]::RE_7ZIPZSTD_ATTR_STR,
        [ExtracaClass]::RE_7ZIPZSTD_SIZE_STR,
        [ExtracaClass]::RE_7ZIPZSTD_COMPRESSED_SIZE_STR,
        [ExtracaClass]::RE_7ZIPZSTD_PATH_STR
    )

    static [string]$RE_7ZIPZSTD_COL_NAMES=@(
        #"       "       "       "       "             "Name
        "Date", "Time", "Attr", "Size", "Compressed", "Path"
    )

    # -- definition for task
    $tasks= @{}

    # -- for the determined operation 

    # -- fileinfo - key by path
    static [hashtable]$fi=@{
        # stores all paths seen by the class
    }

    # -- paths
    static [hashtable]$p=@{

        # TODO: change to user home dir
        log = "D:\usr\var\local\extraca\logs"
        
        # TODO: ask for temp locations when switched used
        #temp = @("H:\tmp\_tmp\extraca\d", "K:\tmp\_tmp\extraca\d")

        # the source path
        SRC= @()
        SRC_NORMALISED = @()

        # the destination path of the operation
        TARGET= @()
        NORMALISED_TARGET = @()
    }

    #endregion ex-class-var
    # -----------------------------------------------------------------------------------
    #region ex-class-constructor
    ExtracaClass(
        # derived arguments
        [hashtable]$ARGS_HASHTABLE,

        # derived options
        [hashtable]$OPTIONS_HASHTABLE
    ) {

    }

    ExtracaClass(
    ) {
        $script:L.i("CLASS:ExtracaClass.ExtracaConstructor()")
        # for each thing - operations
        #[ExtracaOperation]$task
        # $this.tasks[ExtracaClassData]::new()
        # $this.init(
        #     $this.task.p.SRC_FILE,
        #     $this.task.p.TARGET_DIR,
        #     $this.task.d.ARGS
        # )
        # once the flags have been set we know whether to start the processing
        if ($this.f.determine_operation {$this.determine{_operation()s}}
        if ($this.f.is_valid_operation) {$this.start()}
    }
    [void]init() {
        $this.CheckDependencies()
    }
    #endregion ex-class-constructor
    # -----------------------------------------------------------------------------------
    #region ex-class-messages

    [void]ShowStartMessage() {
        $script:L.b()
        $script:L.i(@("///EXTRACA/\\","STARTED"))
        $script:L.i(@("  Starting Memory Usage:",(Get-MemUsage)))
        $script:L.b()
        $script:L.i(@("TASK:"))
        $script:L.i(@("  SRC_PATH: ", $this.d.SRC_PATH))
        $script:L.i(@("  TARGET_PATH: ", $this.d.TARGET_PATH))
        $script:L.b()
    }

    static [void]ShowEndMessage() {
        Write-Progress -Activity "Finished!" -PercentComplete 100
        $script:L.b()
        $script:L.i(@("//EXTRACA\\","Finished!"))
        $script:L.b()
    }

    static [void]ShowDir([string]$path) {Get-Item $path}

    static [void]ShowResults([string]$path) {[ExtracaClass]::ShowDir($path)}

    [void]ShowFlags($index) {
        # $types=@('arc_flags, op_flags')
        if($index -gt 0) {
            foreach ($key in $this.flags) {
                $value= $this.flags[$key]
                Write-Debug "Flags: `$this.flags[$key]= $value"
            }
        }

        foreach ($key in $this.arc_flags) {
            $value= $this.arc_flags[$key]
            Write-Debug "Arg Flag: `$this.flags[$key]= $value"
        }

        foreach ($key in $this.op_flags) {
            $value= $this.op_flags[$key]
            Write-Debug "Op Flag: `$this.flags[$key]= $value"
        }

    }

    static [void]ShowPaths() {
        #foreach ($path in $this.SESSION.$p) {}
    }

    #endregion ex-class-messages
    # -----------------------------------------------------------------------------------
    #region ex-class-validation
    static [string]TestExecutableInPath(
        [string]$exe_name
    ) {
        $system_paths= [Environment]::GetEnvironmentVariable(
            "PATH", [System.EnvironmentVariableTarget]::Machine
        ).Split(";")
        $user_paths= [Environment]::GetEnvironmentVariable(
            "PATH", [System.EnvironmentVariableTarget]::User
        ).Split(";")
        $combined_paths= $system_paths + $user_paths
        $executable_found= $False
        $exe_path= ""
        foreach ($test_path in $combined_paths) {
            # Check if the path is not empty
            if (-not [string]::IsNullOrWhiteSpace($test_path)) {
                $full_path= Join-Path -Path $test_path -ChildPath $exe_name
                if (Test-Path -Path $full_path -Type Leaf) {
                    $exe_path= $full_path
                    $executable_found= $True
                    $script:L.i(@("TestExecutableInPath: '$exe_name' found at: ", $exe_path))
                    break  # Exit the loop if found
                }
            }
            else {$script:L.w(@("TestExecutableInPath: Empty or null path found. Ignore"))}
        }
        if (-not $executable_found) {
            $script:L.e(@("TestExecutableInPath: '$exe_name' does not exist in the PATH."))
        }
        return $exe_path
    }

    static [void]CheckDependencies() {
        foreach ($exe_name in [ExtracaClass]::MANDATORY_DEPS) {
            $script:L.i(@("CheckDependencies: current:", $exe_name))
            $exe_path= TestExecutableInPath($exe_name)
            if (-not $exe_path) {
                $script:L.e(@("CheckDependencies: Exiting because '$exe_name' was not found in the PATH."))
                exit 1
                break
            }
            else {[ExtracaClass]::EXE_PATHS[$exe_name] = $exe_path}
        }
        $script:L.b()
    }
    #endregion 
    
    #region ex-class-file
    static [string]_GetHashForDir(
        [string]$arc_path
    ) {
        $HashString = (Get-ChildItem $arc_path -Recurse | Get-FileHash -Algorithm MD5).Hash | Out-String
        #$this.d.target_dir_hash = $HashString
        return $HashString
    }

    static [void]_CreateDirIfMissing([string]$path) {
        if (-not (Test-Path -Path $path)) {
            $script:L.w(@("Creating missing log directory:", $path))
            New-Item -Force -Type Directory -Path $path
        }
    }

    [void] _GetFileInfo($paths) {
        foreach($p in $paths) {$this.fi[$p]= Get-ChildItem -Path $p}
    }

    # get or create a temporary folder
    [string]GetTemporaryFolder(
        [string]$name="extraca",
        [string]$rootpath
    ) {
        $folderName = ".extraca"
        $filePath = ""
        if ($rootpath) {
            $filePath+=$rootPath
        } else {
            $filePath=$Env:TEMP
        }
        if ($name) {
            $folderName+=$name
        } else {
            $folderName+="extraca-$(New-Guid)"
        }
        $tempFolderPath = Join-Path $rootpath $folderName
        New-Item -Type Directory -Path $tempFolderPath | Out-Null
        return $tempFolderPath
    }

    [object]GetFi($p){
        return [ExtracaClass]::fi[$p]
    }

    #endregion ex-class-file
    # -----------------------------------------------------------------------------------
    #region ex-class-processing
    # - apply rules to a processing for a certain archive type
    # -- extract first dir if preset with text
    
    #region ex-class-flags
    [string]GetTempBestChoice() {
        [string] $temp_path_bestchoice = ""
        # for each drive - find free space and use that one, _most of the time_
        # the other factor is if most recent used rotate it

        # sets has_free_space
        # sets has_not_been_used_recently
        return $temp_path_bestchoice
    }
    
    # ($src_fi) {
    #         $this.data.src_is_directory= $True
    #         $this.flags.src_is_file = $False
    #     } else {
    #         $this.flags.src_is_directory = $False
    #         $this.flags.src_is_file = $True
    #     }
    #     if($this.data.SRC_PATH_FI.Count -eq 1) {
    #         $this.flags.src_has_one_file = $True
    #     }
    #     if($this.flags.src_has_one_file -eq $False) {
    #         $this.flags.src_has_many_files = $True
    #     }
    #     # has matching files
    #     #if()

    [void]SetOperationFlags() {
        $this.job.fi["source"] = $this.GetFi($this.data.src_path)
        $this.job.fi["source"] = $this.GetFi($this.data.src_path)
    }

    [void]_SetArchiveFlags() {
        $script:L.i("Class:ExtracaClass._SetArchiveFlags()")
        # #TODO: change to root_
        # if ($this.data.dir_count -eq 1) {
        #     $this.flags.has_one_dir= $True
        #     $this.flags.has_many_dirs= $False
        # }
        # if ($this.data.dir_count -gt 1) {
        #     $this.flags.has_many_dirs= $True
        #     $this.flags.has_one_dirs= $False
        # }

        # if ($this.data.file_count -eq 1) {
        #     $this.flags.is_one_file= $True
        #     $this.flags.is_many_files= $False
        # }
        # if ($this.data.file_count -gt 1) {
        #     $this.f.is_one_file= $False
        #     $this.f.is_many_files= $True
        #     $script:L.i(@("is Many files..."))
        # }

        # if (
        #     ($this.d.dir_count -gt 1) -or ($this.d.file_count -gt 1)
        # ) {
        #     $script:L.i(@("Valid archive detected..."))
        #     $this.f.is_valid_operation= $True
        # }
        # else {
        #     $script:L.e(@("Archive was not detect as valid"))
        #     $this.f.is_valid_operation= $False
        # }

        # if (-not ($this.f.is_valid_operation)) {exit 1}
    }

    #endregion ex-class-flags
    [void]SetNamingRules() {
        # all extractions should have a named directory... 
        # if the name is missing name it after the archive
        $this.d.name_no_extension = $this.fi.SF_FI.Name
        $this.f.set_name_for_archive = $True
    }

    # ---- operation of path
    [void]Process() {
        # get number of files
        # decide on temporary dirs
        $this.completed_tasks= @{}

        foreach ($task in $this.tasks) {
            $this.job= [ExtracaJob]::new($task)
        }
    }

    [void]ProcessFile() {
        # decide if a name is required based on the src_file name
        # get the age of the oldest file to append
        # create XML, json, csv of files within - to compare with output
        # create hash of each file and directory and archive
        $this.InspectArchive()

        Write-Progress -Activity "Processing based on rules..." -PercentComplete 1

        # for this archive
        $this.SetNamingRules()
        $this.GetTempBestChoice()
        $this.SetArchiveFlags()

        # TODO: ifVerbose
        $this.ShowFlags()
    }
    [void]ProcessDirectory() {
        # for each matching file - prorcess file
    }

    # --- operation of archive
    [void]InspectArchive() {
        $script:L.i(@("Inspecting Archive...."))

        $this._inspectSevenZip()
        #$this.f.if_validate_with_deflate() $this.CONVERT() {} && $this._inspectZip()
    }

    # --- archive inspection of zip
    # deflate - use to compare results
    [void]_inspectZip() {}

    # --- archive inspection of 7zip
    # determine the contents of the archive... 
    [void]_inspectSevenZip() {
        # -ba - suppress headers; undocumented.
        $this.d.list_string_7z= Invoke-Expression '7zS.exe l -ba "$this.d.SRC_FILE"' | Format-Wide | Out-String
        
        # 2006-03-09 13:12:53             442324       177220  31 files, 38 folders
        $this.d.file_count= ($this.d.list_string_7z | `
            Select-String -CaseSensitive -Pattern "^.+([0-9]+)\s{1,}files$").Matches.Groups[1]

        # 2006-03-09 13:12:53             442324       177220  31 files, 38 folders
        $this.d.dir_count= ($this.d.list_string_7z | `
            Select-String -CaseSensitive -Pattern "^.+([0-9]+)\s{1,}folders$").Matches.Groups[1]

        if ($this.d.file_count -eq 0) {
            $script:L.e(@("Archive $($this.d.CURRENT_ARCHIVE.Name)", " has ", "zero files"))
            break
        } 
        else {
            $script:L.i(@("Archive FileCount = ", $this.r.file_count))
        }
    }

    # convert the content into the format
    [void]_ConvertArchiveListViewInto(
        [array]$formats
    ) {
        $outputs= @()
        foreach($f in $formats) {
            # hashtable into:-
            $outputStr = ""
            
            if ($f -eq "CSV") {
                # TODO: pre insert column names first by using records lol
                $outputStr= Join-String [ExtracaClass]::RE_7ZIPZSTD_COL_NAMES, ","
                foreach ($group in $matches) {
                    # TODO: set is directory
                    $outputStr+=""
                }
            }

            # turn hashtable into:- 
            if ($f -eq "JSON") {
                $outputStr= ConvertTo-Json 
            }

            if ($f -eq "XML") {
                $outputStr= ConvertTo-XML
            }

            $outputs+= $outputStr
        }
    }
    # get teh files within the archive 
    [void]_GetFilesFromListViewWithinArchive(
        [string]$text_list_output = $this.d.list_string_7z
    ) {
        # use an array list.. to store each parsed date
        [System.Collections.ArrayList]$parsed_dates_ArrayList = @()

        $datetime_expected_string = ""

        # look through the archive to get the first file... and detemrine the age only this way,..
        # 2005-12-24 18:25:52 D....            0            0  bin
        foreach ($line in $text_list_output) {
            # first we need to trim the line to minimum such that our RegExp is easy
            $full_trimmed_line = $line.Substring(0, [ExtracaClass]::LIST_DATETIME_TRIM_LENGTH)
            $parse_exact_line = $line.Substring(0, $full_trimmed_line.length-1)
            
            # now extract and convert the date into DateTime Object .net/ps1
                # -- YYYY-MM-DD HH:MM:SS D....
                # -- YYYY-MM-DD HH:MM:SS ....A
            $datetime_expected_string = $full_trimmed_line | `
                Select-String -CaseSensitive -Pattern `
                [ExtracaClass]::RE_7ZIPZSTD_DATETIME_STR
            #TODO: show the groups matched
                
            # now convert the date into a datetime
            $parsed_dates_ArrayList.add(`
                ([datetime]::ParseExact(`
                    $parse_exact_line,`
                    "YYYY-MM-DD HH:MM:SS",`
                    [Globalization.CultureInfo]::CreateSpecificCulture("en-GB")`
                ))
            )
        }

        $this.d.datetime_expected_string = $datetime_expected_string
        $this.d.parsed_dates_ArrayList = $parsed_dates_ArrayList
    }

    # -- inspect ops
    # for a set of paths if the path has nothing but another directory flatten useless nesting
    # the dirs must be of same name
    [void]_archivePathsFlatten() {
        # this will replace a file list with the paths corrected
    }

    # get the oldest and newest date
    [datetime]_PathsDateStats(){
        $newest_datetime = $Null
        # compare the date starting at 1 with index -1
        $oldest_datetime = $Null
        $count = $this.d.parsed_dates_ArrayList.Count
        for ($i = 0; $i -lt $count; $i++) {
            $current_date = [DateTime]$this.d.parsed_dates_ArrayList[$i]
            if (($Null -eq $newest_datetime) -or ($current_date -gt $newest_datetime)) {
                $newest_datetime = $current_date
            }
            if (($Null -eq $oldest_datetime) -or ($current_date -lt $oldest_datetime)) {
                $oldest_datetime = $current_date
            }
        }

        return @($oldest_datetime, $newest_datetime)
    }
    # -- archive ops
    [string]TOZIP(
        [string]$arc_path
    ) {
        # TODO: this needs to only create if it doesnt exist, conversely it should cleanup
        $TARGET_DIR = $this.GetTemporaryFolder("C:\t\extraca")
        $this._ARC_ExtractAllFiles($TARGET_DIR, $arc_path)
        $this._ARC_CompressDir($TARGET_DIR, $arc_path)
        return $TARGET_DIR
        # clear
    }
    [void]CONVERT(
        [string]$arc_path,
        [string]$format,
        [string]$target_path
    ){

    }
    [void]COMPRESS(
        [string]$src_path,
        [string]$arc_path
    ) {
        # TODO: zip format
        Invoke-Expression '7sS.exe a -y -bb3 -mx9 "$arc_path" "$src_path"'
    }
    [void]EXTRACT(
        [string]$TARGET_DIR,
        [string]$arc_path
    ) {
        Invoke-Expression '7zS.exe e -y -bb3 "$arc_path" -o "$TARGET_DIR"'
    }
    [void]CLEANUP(
        [hashtable]$CLEANUP_PATHS
    ) {
        # for each path created clean it up .. 

    }
    #endregion ex-class-processing
    # ------------------------------------------------------------------------------------
    # start processing...
    start() {
        Write-Progress -Activity "Create Missing Dirs..." -PercentComplete 0
        $this._CreateDirIfMissing($this.paths["log"])
        $this._CreateDirIfMissing($this.paths["temp"])
        Write-Progress -Activity "Setting Temporary Location..." -PercentComplete 0.3

        ShowDetails # start details
        $this.SetOperationRules()

        # ALWAYS
        Write-Progress -Activity "Processing archives..." -PercentComplete 0.5
        $this.Process()

        # flatten the file paths if enabled removing empty or directories with directories within it 
        # of the same name ( not from root )
    }
}
#endregion ex-class
# ---------------------------------------------------------------------------------------
# ---- SCRIPT SCOPE 
#region ex-script-scope
if (-not (Test-Path -Path $SRC_PATH)) {
    Write-Host "Local-Scope: SRC_FILE does not exist or is not accessible."
    exit 1
}
# if (-not (Test-Path -Path $TARGET_DIR)) {
#     Write-Host "Local-Scope: TARGET_DIR does not exist or is not accessible."
#     exit 1
# }
# ---------------------------------------------------------------------------------------
$globalTaskObject = [ExtracaClass]::new(
    @{
        SRC_PATH = $SRC_PATH
        TARGET_PATH = $TARGET_PATH
    },
    # $TARGET_DIR,
    @{
        PROFILE = 
        $PSCmdlet.MyInvocation.BoundParameters["PROFILE"].IsPresent
    }
)
#endregion ex-script-scope

