REM uses contig from sysinternals really nice way of approaching this
REM i notice afterwards they run defrag.exe to clean up and compact files

ECHO welcome to the automated defrag script! hope you have a nice time
ECHO.
ECHO.this script defrags windows now and again...
ECHO.
REM -q quiet, -v verbose, -a analyse defragmentation, -s recurse subdirectories
REM contig.exe -q -v -s
ECHO.
ECHO devxp
CALL defragDrive.cmd e: