import { parseArgs } from 'node:util'
import fs from 'node:fs/promises'
import crypto from 'node:crypto'

/**
 * Parses WAV file metadata from its buffer.
 * @param {Buffer} fileBuffer - The buffer containing the WAV file data.
 * @returns {object} An object with sampleRate, blockAlign, dataOffset, and dataSize.
 */
function parseWavMetadata(fileBuffer) {
    if (fileBuffer.length < 44) 
        throw new Error('File is too small to be a valid WAV file.')
    if (
        fileBuffer.toString('ascii', 0, 4) !== 'RIFF' || 
        fileBuffer.toString('ascii', 8, 12) !== 'WAVE'
    ) {
        throw new Error('Not a valid WAV file (RIFF/WAVE markers not found).')
    }

    let fmtChunk = null
    let dataChunkInfo = { offset: -1, size: 0 }
    let currentOffset = 12 // Start after 'RIFF', overall_size, 'WAVE'

    // Need at least 8 bytes for chunk ID and size
    while (currentOffset < fileBuffer.length - 8) { 
        const chunkId = fileBuffer.toString('ascii', currentOffset, currentOffset + 4)
        const chunkSize = fileBuffer.readUInt32LE(currentOffset + 4)
        const chunkDataStart = currentOffset + 8

        if (chunkDataStart + chunkSize > fileBuffer.length && chunkId !== 'data' && chunkId !== 'fmt ') {
            // If a chunk claims to extend beyond the buffer and it's not a critical one we are parsing,
            // it might be an issue with a very short file or truncated last chunk.
            // We primarily care about fmt and data.
            console.warn(`Warning: Chunk '${chunkId}' size ${chunkSize} from offset ${currentOffset} may exceed buffer length ${fileBuffer.length}. Attempting to continue parsing.`)
        }
        
        if (chunkId === 'fmt ') {
            if (chunkDataStart + 16 > fileBuffer.length) 
                throw new Error('Malformed fmt chunk (too small for PCM basic fields).')
            fmtChunk = {
                audioFormat: fileBuffer.readUInt16LE(chunkDataStart),
                numChannels: fileBuffer.readUInt16LE(chunkDataStart + 2),
                sampleRate: fileBuffer.readUInt32LE(chunkDataStart + 4),
                // Required for some calculations
                byteRate: fileBuffer.readUInt32LE(chunkDataStart + 8),
                // Bytes per sample frame
                blockAlign: fileBuffer.readUInt16LE(chunkDataStart + 12),
                bitsPerSample: fileBuffer.readUInt16LE(chunkDataStart + 14),
            }
            if (fmtChunk.audioFormat !== 1) throw new Error('Only PCM WAV files are supported (AudioFormat !== 1).')
        } 
        else if (chunkId === 'data') {
            dataChunkInfo = {
                offset: chunkDataStart,
                size: chunkSize
            }
            // If fmt chunk is already found, we can often break, assuming 'data' is the last primary chunk of interest.
            // However, WAVs can have LIST chunks etc. after 'data'. We primarily need 'data' offset and size.
            if (fmtChunk) break 
        }
        
        currentOffset = chunkDataStart + chunkSize
        // RIFF chunks are word-aligned. If chunkSize is odd, there's a padding byte.
        if (chunkSize % 2 !== 0) {
            currentOffset++
        }
    }

    if (!fmtChunk) 
        throw new Error('fmt chunk not found or incomplete in WAV file.')
    if (dataChunkInfo.offset === -1)
        throw new Error('data chunk not found in WAV file.')

    // Validate data chunk size against buffer length
    if (dataChunkInfo.offset + dataChunkInfo.size > fileBuffer.length) {
        console.warn(`Warning: Declared data chunk size (${dataChunkInfo.size}) at offset ${dataChunkInfo.offset} exceeds actual file buffer length (${fileBuffer.length}). Adjusting data size to fit buffer.`)
        dataChunkInfo.size = fileBuffer.length - dataChunkInfo.offset
    }
    if (dataChunkInfo.size < 0) dataChunkInfo.size = 0


    return {
        sampleRate: fmtChunk.sampleRate,
        blockAlign: fmtChunk.blockAlign,
        dataOffset: dataChunkInfo.offset,
        dataSize: dataChunkInfo.size
    }
}


/**
 * This script corrupts a WAV file by randomly changing intervals of bytes,
 * attempting to follow a musical rhythm.
 * - Corruption events are triggered based on a specified rhythm (time signature and BPM).
 * - For each rhythmic pulse, it performs [often] corruptions.
 * - Each corruption changes a number of bytes related to [corruptionLength].
 * - The parameters [often], [corruptionLength], and [bpm] themselves randomly mutate over time.
 */
async function performRhythmicCorruption({
    inputFile,
    often,
    corruptionLength,
    rhythmSignature, // e.g., '4/4'
    bpm,
    outputFileArg = undefined
}) {
    let currentOften = often
    let currentCorruptionLength = corruptionLength
    let currentBpm = bpm
    const inputBasename = inputFile.split(/[\\/]/).pop()
    const effectiveOutputFilePath = outputFileArg || `corrupted_rhythmic_${inputBasename}`

    console.log(`Output file will be: ${effectiveOutputFilePath}`)
    console.log('Starting rhythmic corruption process...')

    try {
        const fileBuffer = await fs.readFile(inputFile)
        const mutableBuffer = Buffer.from(fileBuffer)

        const { sampleRate, blockAlign, dataOffset, dataSize } = parseWavMetadata(fileBuffer)

        if (dataSize <= 0) {
            console.warn('Warning: No audio data found in WAV file or data size is zero.')
            await fs.writeFile(effectiveOutputFilePath, mutableBuffer)
            console.log(`File copied to ${effectiveOutputFilePath} as no data was available for corruption.`)
            return
        }

        const bytesPerSecond = sampleRate * blockAlign
        if (bytesPerSecond <= 0) throw new Error('Invalid WAV properties: BytesPerSecond is zero or negative.')

        const [beatsPerMeasureStr, beatUnitStr] = rhythmSignature.split('/')
        // X from X/Y (e.g. 4 in 4/4) - for context/logging
        const beatsPerMeasure = parseInt(beatsPerMeasureStr, 10)
        // Y from X/Y (e.g. 4 in 4/4, 8 in 6/8) - defines the pulse
        const beatUnit = parseInt(beatUnitStr, 10)

        if (isNaN(beatsPerMeasure) || isNaN(beatUnit) || beatsPerMeasure <= 0 || beatUnit <= 0)
            throw new Error(`Invalid rhythm signature: ${rhythmSignature}. Expected format like '4/4' or '6/8'.`)

        // Tracks position in data chunk, in bytes, by rhythmic pulses
        let currentPulseBytePosition = 0
        let totalPulsesProcessed = 0

        // Calculate properties for the current pulse
        while (currentPulseBytePosition < dataSize) {
            // Ensure BPM is at least 1
            const secondsPerPulse = 60.0 / Math.max(1, currentBpm)
            // Ensure at least 1 byte per pulse
            const bytesPerPulse = Math.max(1, Math.round(secondsPerPulse * bytesPerSecond)) 

            // Log roughly every couple of measures
            if (totalPulsesProcessed % (beatsPerMeasure * 2) === 0) {
                const measureCount = Math.floor(totalPulsesProcessed / beatsPerMeasure)
                console.log(`\nMeasure ~${measureCount + 1}, Pulse ${totalPulsesProcessed % beatsPerMeasure + 1}/${beatsPerMeasure}. BPM: ${currentBpm.toFixed(1)}. Often: ${currentOften}. CorruptLen: ${currentCorruptionLength}.`)
            }
            
            // Perform 'currentOften' corruptions, triggered by this pulse
            for (let j = 0; j < currentOften; j++) {
                // Determine a random starting position for corruption within the data part of the file.
                const corruptionByteOffsetInData = Math.floor(Math.random() * dataSize)
                const corruptionStartAbsolute = dataOffset + corruptionByteOffsetInData
                
                const maxPossibleLengthAtPosition = (dataOffset + dataSize) - corruptionStartAbsolute
                if (maxPossibleLengthAtPosition <= 0) continue

                // Cap max individual corruption
                const dynamicMaxCorruptLen = Math.min(Math.max(1, currentCorruptionLength), 128) 
                let actualCorruptionLength = Math.floor(Math.random() * dynamicMaxCorruptLen) + 1
                actualCorruptionLength = Math.min(actualCorruptionLength, maxPossibleLengthAtPosition)
                
                if (actualCorruptionLength <= 0) continue

                const randomBytes = crypto.randomBytes(actualCorruptionLength)
                randomBytes.copy(mutableBuffer, corruptionStartAbsolute, 0, actualCorruptionLength)
            }

            // Mutate parameters for the next pulse
            // -0.3 to 0.3
            const oftenChangeFactor = (Math.random() - 0.5) * 0.6
            currentOften = Math.max(1, Math.round(currentOften + currentOften * oftenChangeFactor + (Math.random() * 2 - 1)))
            currentOften = Math.min(currentOften, 500)

            const lengthChangeFactor = (Math.random() - 0.5) * 0.6
            currentCorruptionLength = Math.max(1, Math.round(currentCorruptionLength + currentCorruptionLength * lengthChangeFactor + (Math.random() * 4 - 2)))
            currentCorruptionLength = Math.min(currentCorruptionLength, 256)

            // BPM changes a bit less aggressively
            const bpmChangeFactor = (Math.random() - 0.5) * 0.4
            // Add some absolute jitter Matt: scale with 0.4
            currentBpm += currentBpm * bpmChangeFactor + (Math.random() * 10 - 5) * 0.4
            // Ensure BPM is within a reasonable range
            currentBpm = Math.max(20, Math.min(currentBpm, 600)) // BPM range 20-600

            currentPulseBytePosition += bytesPerPulse
            totalPulsesProcessed++
        }

        console.log(`\nRhythmic corruption complete. Corrupted file saved as ${effectiveOutputFilePath}`)

        return await fs.writeFile(effectiveOutputFilePath, mutableBuffer)
    } 
    catch (error) {
        console.log()
        console.error('An error occurred during the rhythmic corruption process:')
        if (error.code === 'ENOENT')
            console.error(`Error: Input file '${inputFile}' not found or not accessible.`)
        else 
            console.error(error.message)

        // For debugging, re-throw if it's not a known type of operational error
        if (
            error.code !== 'ENOENT' && 
            !(error instanceof Error && 
                (
                    error.message.includes('WAV') || 
                    error.message.includes('rhythm')
                )
            )
        ) throw error 
        
        console.log()
    }
}

// --- Script Entry Point ---
const { values: cliValues, positionals: cliPositionals } = parseArgs({
    options: {
        often: { type: 'string', short: 'o' },
        // Changed from 'size' to 'length'
        length: { type: 'string', short: 'l' },
        rhythm: { type: 'string', short: 'r' },
        bpm: { type: 'string', short: 'b' },
        output: { type: 'string', short: 'f' }
    },
    allowPositionals: true,
    strict: false
})

const inputFile = cliPositionals[0]

// Validate arguments
if (!inputFile) {
    console.error('Error: Input file path is required.')
    console.log('Usage: node script.js <inputfile.wav> --rhythm <X/Y> --bpm <number> -o <often_value> -l <length_value> [-f <outputfile.wav>]')
    process.exit(1)
}

// const requiredArgs = ['often', 'length', 'rhythm', 'bpm']
// for (const arg of requiredArgs) {
//     if (!cliValues[arg]) {
//         console.error(`Error: --${arg} is a required argument.`)
//         console.log('Usage: node script.js <inputfile.wav> --rhythm <X/Y> --bpm <number> -o <often_value> -l <length_value> [-f <outputfile.wav>]')
//         process.exit(1)
//     }
// }

const initialOftenNum = parseInt(cliValues.often, 10)
const initialCorruptionLengthNum = parseInt(cliValues.length, 10)
const initialBpmNum = parseFloat(cliValues.bpm)
const rhythmSignatureStr = cliValues.rhythm

if (isNaN(initialOftenNum) || initialOftenNum <= 0) {
    console.error('Error: --often (-o) must be a positive integer.')
    process.exit(1)
}
if (isNaN(initialCorruptionLengthNum) || initialCorruptionLengthNum <= 0) {
    console.error('Error: --length (-l) must be a positive integer.')
    process.exit(1)
}
if (isNaN(initialBpmNum) || initialBpmNum <= 0) {
    console.error('Error: --bpm (-b) must be a positive number.') 
    process.exit(1)
}
if (!/^\d+\/\d+$/.test(rhythmSignatureStr)) {
    console.error('Error: --rhythm (-r) must be in X/Y format (e.g., "4/4", "6/8").') 
    process.exit(1)
}


console.log()
console.log('--- Effective Arguments ---')
console.log()
const effectiveArgs = {
    inputFile: inputFile,
    rhythm: rhythmSignatureStr || '4/4',
    bpm: initialBpmNum || 120,
    often: initialOftenNum || 2,
    corruptionLength: initialCorruptionLengthNum || 16,
    outputFile: cliValues.output || `corrupted_rhythmic_${inputFile.split(/[\\/]/).pop()}`
}
console.table(effectiveArgs)
console.log('---------------------------')

performRhythmicCorruption(effectiveArgs).catch(err => {
    console.error('FATAL SCRIPT ERROR:', err.message)
    console.error(err.stack)
    process.exit(1)
})

console.log()

