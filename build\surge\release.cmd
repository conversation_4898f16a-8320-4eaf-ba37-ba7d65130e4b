@echo off
popd .
I:
CD \lib\local\surge\surge\build\src\surge-xt\surge-xt_artefacts\Release\VST3\Surge XT.vst3
DIR .
REM C:\Program Files\Common Files\VST3\Surge Synth Team\SurgeXTData
REM C:\Program Files\Common Files\VST3\Surge Synth Team\Surge XT.vst3
REM 

rm -v I:\lib\local\surge\last-surge-vst3.zip
zip -9 -r I:\lib\local\surge\last-surge-vst3.zip "C:\Program Files\Common Files\VST3\Surge Synth Team\Surge XT.vst3"
DIR I:\lib\local\surge\last-surge-vst3.zip
MKDIR "C:\Program Files\Common Files\VST3\Surge Synth Team\Surge XT.vst3"
DEL /s /q "C:\Program Files\Common Files\VST3\Surge Synth Team\Surge XT.vst3\*"
REM rm -fR "C:\Program Files\Common Files\VST3\Surge Synth Team\Surge XT.vst3\*"
REM I:\lib\local\surge\surge\build\src\surge-xt\surge-xt_artefacts\Release\VST3\Surge XT.vst3
XCOPY /s /e /v /y . "C:\Program Files\Common Files\VST3\Surge Synth Team\Surge XT.vst3\"

REM cp -Rv ".\Surge XT.vst3\*" "C:\Program Files\Common Files\VST3\Surge Synth Team\Surge XT.vst3\"
DIR "C:\Program Files\Common Files\VST3\Surge Synth Team\Surge XT.vst3\"
DIR "C:\Program Files\Common Files\VST3\Surge Synth Team\"
PUSHD .