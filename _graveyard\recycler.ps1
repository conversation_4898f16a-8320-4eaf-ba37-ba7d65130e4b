param (
    [Parameter(Mandatory=$true)]
    [string]$filePath
)

$fileInfo = Get-Item $filePath
$drive = ($fileInfo).PSDrive.Root
#Write-host $drive
$recycleBinPath = Join-Path $drive -ChildPath "`$Recycle.Bin"
Write-host Path: $recycleBinPath
Get-Item $filePath
if (Test-Path -Path $recycleBinPath) {
    #Move-Item $fileInfo.FullName -Destination $recycleBinPath
	D:\usr\bin\nircmd.exe moverecyclebin $fileInfo.FullName
    Write-Output "Successfully recycled file: $filePath, next should fail"
	Get-Item $filePath
} else {
    Write-Output "Failed to recycle file: $filePath"
    Write-Output "Recycle Bin path '$recycleBinPath' does not exist."
}
