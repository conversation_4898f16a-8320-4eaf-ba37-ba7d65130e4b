[CmdletBinding(
    PositionalBinding = $False
)]
# the path to process
param (
    [Parameter(Mandatory=$True, Position=0)]
    [string]$TARGET_DIR,

    [Parameter(Mandatory=$False)]
    [switch]$RECURSIVE
)

$PSEP = [System.IO.Path]::DirectorySeparatorChar
$ESCAPED_PSEP = [Regex]::Escape($PSEP)
$invalid_shortcuts = @{}

Write-Host "PSEP: $PSEP"
Write-Host "ESCAPED_PSEP: $ESCAPED_PSEP"


Function ProcessShortcut($item) {
    $path = $item.FullName
    if (-not (Test-Path -Path $path -IsValid)) {
        Write-Host "The path does not exist"
        exit 1
    }

    $file_info = (Get-Item -Path $path)
    $file_name = $file_info.Name.ToString

    # a standard form shortcut is of the form: __[DRIVE_LETTER]_[Part]_[Part]..._[Part].lnk
    # to match a path like __X_a_coll.lnk or __W_pool_au_inst_prj.lnk

    $is_already_matching = $file_name -Match "^([_]{2}[A-Z]{1}){1}(_\w+)+\.lnk$"
    # check the regexp works:=
    if("__X_a_coll.lnk" -Match "^([_]{2}[A-Z]{1}){1}(_\w+)+\.lnk$") {
        Write-Host "Matched"
    } else {
        Write-Host "Not Matched"
        exit 1
    }

    # Get the shortcut object
    $shortcut = (New-Object -ComObject WScript.Shell).CreateShortcut($path)

    # Get the target path
    $shortcut_target_path = $shortcut.TargetPath

    Write-Host "Processing: shortcut_target_path $shortcut_target_path from $path"

    # Check if the target path exists and the shortcut is valid
    if (-not (Test-Path -Path $shortcut_target_path)) {
        Write-Host "Shortcut target does not exist: $shortcut_target_path needs to be updated"
        $invalid_shortcuts[$path]=@{
            file_name = $file_name
            path = $path
            target = $shortcut_target_path
            is_already_matching = $is_already_matching
        }
        return
    }

    if (-not $is_already_matching) {
        Write-Host "Not in standard form"

        # Get file information from the target path instead of the file name
        $target_fi = (Get-Item -path $shortcut_target_path)

        # Construct the new shortcut name (Example: __[DRIVE_LETTER]_[FirstPartOfFileName]..[PartOfFileName].lnk)

        $root = $target_fi.Root.Name
        $root = $root -Replace ":", ""
        $root = $root -Replace "$ESCAPED_PSEP", ""
        Write-Host "Root: $root"

        $name = $target_fi.ResolvedTarget
        Write-Host "Name: $name"
        
        $remaining_path = $name -Replace "([_]{1}\w+)+", $1
        Write-Host "Remaining Path: $remaining_path"

        $current_path = $remaining_path
        #Write-Host "Rest of Path: $current_path"

        # check if there is more depth using the matches object
        $has_remaining_path = $remaining_path -Match "([/w+]){1}"+$ESCAPED_PSEP
        Write-Host "Has Remaining Path: $has_remaining_path"

        if ($has_remaining_path) {
            foreach($match in $matches) {
                Write-Host "Match: $match"
            }
            $current_path += $remaining_path -Replace "([/w+])"+$ESCAPED_PSEP+"*", ""
            Write-Host "Current path: $current_path"
            Write-Host "Remaining path: $remaining_path"
            
        } else {
        }
        #$new_FullName = $new_root + $target_fi.FullName -replace '^([^_]+)_.*$', '$1'

        Write-Host "New Path: $current_path"

        # # Update the shortcut target 
        # $shortcut.TargetPath = Join-Path -Path $target_fi.Directory.FullName -ChildPath $new_shortcut_name
        #$shortcut.Save()
    } 
    else {
        Write-Host "Already in standard form"
        return
    }
}

# for a directory get all windows shortcuts
Function GetAllShortcuts($dir_path, $options=@{recurse = $False}) {
    # check if the path is of the type file, cannot process a file
    $dir_path_fi = (Get-Item -Path $dir_path)
    if ($dir_path_fi -is [System.IO.FileInfo]) {
        Write-Host "The path is a file, cannot process a file"
        exit 1
    }

    $shortcut_options = @{
        Filter = "*.lnk"
        Path = $dir_path
    }
    if ($options.recurse) {
        Write-Host " Added: recurse"
        $shortcut_options.Add("Recurse", $True)
    } else {
        Write-Host " No recurse...."
    }

    $dir = Get-ChildItem @shortcut_options

    Write-Host "Found: $($dir.Length) shortcuts"

    foreach($file in $dir) {
        Write-Host "$file in $dir_path"
        ProcessShortcut($file)
    }
}

Write-Host ("Target Dir: ", $TARGET_DIR)

GetAllShortcuts($TARGET_DIR)
