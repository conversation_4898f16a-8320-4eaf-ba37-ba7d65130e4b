import {test} from 'tap'
import { exec } from 'child_process'
import * as fs from 'fs/promises'
import path from 'path'

test('disk2sql.mjs should run successfully and create the SQLite DB', async (t) => {
  const testDir = path.join(__dirname, 'testDir')
  const dbPath = path.join(testDir, 'ignore', '0', 'test.db')

  // Step 1: Set up the directory and files
  await fs.rm(testDir, { recursive: true, force: true })
  const targetDir = path.join(testDir, '0/1/2/3/4/5/6/7')
  await fs.mkdir(targetDir, { recursive: true })

  // Create files in every directory along the path
  for (let i = 0; i <= 7; i++) {
    const currentDir = path.join(testDir, '0', '1', '2', '3', '4', '5', '6', String(i))
    await fs.mkdir(currentDir, { recursive: true })
    // Create 0.txt, 1.txt, ..., 7.txt in each directory
    for (let j = 0; j <= 7; j++) {
      await fs.writeFile(path.join(currentDir, `${j}.txt`), `Test file ${j}`)
    }
  }

  // Step 2: Execute the disk2sql.mjs script with the test args
  const command = `node disk2sql.mjs --from ${path.join(testDir, '0')} --to ${path.join(testDir, 'ignore/0')}`

  // Run the command and check if it completes successfully
  await new Promise((resolve, reject) => {
    exec(command, (err, stdout, stderr) => {
      if (err) {
        reject(stderr || err.message)
      } else {
        resolve(stdout)
      }
    })
  })

  // Step 3: Check that the database file exists
  try {
    await fs.access(dbPath)
    t.pass('SQLite database was created')
  } catch (err) {
    t.fail(`Database was not created: ${err.message}`)
  }
})
