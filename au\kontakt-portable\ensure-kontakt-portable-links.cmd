@ECHO OFF
SETLOCAL ENABLEDELAYEDEXPANSION

DIR D:\a\red\K\*KontaktPortable*

DIR "C:\Program Files\VstPlugins\Kontakt * Portable x64*"

FOR /F "delims=" %%i IN ('DIR /B /S D:\a\red\K\*KontaktPortable*') DO (
	ECHO.
	SET "kontakt=%%i"
	REM match the version numbers from the dir: `KontaktPortable_v821` using for
	FOR /F "tokens=2 delims=_v" %%j IN ("!kontakt!") DO (
		SET "version=%%j"
	)	
	ECHO !version!
	ECHO !kontakt!

	REM the symlinks in C:\Program Files\VstPlugins\ are in this form:- `Kontakt 8-2-1 Portable x64` add the -
	REM set it now to the directory so it is minimum 

	SET "linkname=Kontakt !version:~0,1!-!version:~1,1!-!version:~2,1! Portable x64\Kontakt !version~0,1!\x64\vst"
	ECHO Using link: !linkname!

	REM check the source and the target exist
	IF EXIST "!kontakt!" (
		ECHO Source: !kontakt! exists
	) ELSE (
		ECHO Source: !kontakt! does not exist
		EXIT /BA
	)
	IF EXIST "C:\Program Files\VstPlugins\!linkname!" (
		ECHO Target: "C:\Program Files\VstPlugins\!linkname!" exists.. nothing to do
	) ELSE (
		ECHO Target: "C:\Program Files\VstPlugins\!linkname!" does not exist
		ECHO MKLINK /J "C:\Program Files\VstPlugins\!linkname!" "!kontakt!"
		MKLINK /J "C:\Program Files\VstPlugins\!linkname!" "!kontakt!"
	)
	ECHO.
) 

ENDLOCAL