[CmdletBinding(
    PositionalBinding= $False
)]
param (
    [Parameter(Mandatory= $True)]
    [string]$PROJECT_NAME
)

$new_FLP_DIR = S:\pool\au\d\fl\=auto.CURRENT=\ + $PROJECT_NAME
$new_X_POOL_AU_INST_PRJ = X:\pool\au\inst\auto.prj\ + $PROJECT_NAME
$new_W_POOL_AU_INST_PRJ = W:\pool\au\inst\auto.prj\ + $PROJECT_NAME
$new_C_POOL_AU_INST_PRJ = C:\pool\au\inst\auto.prj\ + $PROJECT_NAME
$new_D_POOL_AU_INST_PRJ = D:\pool\au\inst\auto.prj\ + $PROJECT_NAME
$new_S_POOL_AU_INST_PRJ = S:\pool\au\inst\auto.prj\ + $PROJECT_NAME
$new_E_POOL_AU_REC = E:\pool\au\rec\auto.prj\ + $PROJECT_NAME

Function check_drive_exists(
    [String]$path
) {
    try {
        $drive = [System.IO.Path]::GetPathRoot($Path)
        return [System.IO.DriveInfo]::GetDrives() | Where-Object { $_.Name -eq $drive }
    }
    catch {
        # return $False
        exit 1
    }
}

# check the USB disks are actually mounted and didn't fuck up
check_drive_exists($new_FLP_DIR)
check_drive_exists($new_E_POOL_AU_REC)
check_drive_exists($new_X_POOL_AU_INST_PRJ)
check_drive_exists($new_W_POOL_AU_INST_PRJ)
check_drive_exists($new_C_POOL_AU_INST_PRJ)
check_drive_exists($new_D_POOL_AU_INST_PRJ)
check_drive_exists($new_S_POOL_AU_INST_PRJ)

Function exists_or_warn($path) {
    if (-not (Test-Path -Path $path)) {
        New-Item -ItemType Directory $path -Verbose
    } 
    else {
        Write-Warning $path ". Already exists: Cannot continue"
        exit 1
    }    
}

Function create_new_project_dir($path) {
    Write-Information "Create: " $path
    exists_or_warn($path)
    New-Item -Path $path -Confirm $False
}

create_new_project_dir($new_FLP_DIR)
create_new_project_dir($new_X_POOL_AU_INST_PRJ)
create_new_project_dir($new_W_POOL_AU_INST_PRJ)
create_new_project_dir($new_S_POOL_AU_INST_PRJ)
create_new_project_dir($new_C_POOL_AU_INST_PRJ)
create_new_project_dir($new_D_POOL_AU_INST_PRJ)
