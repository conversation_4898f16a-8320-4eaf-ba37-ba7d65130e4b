# for a path - creates in destination all - separate trees based on the files found....

# /some/place => /destination/some/place/some.extension

# we don't want to use a giant foreach... and stream or chunk work instead.. how in powershell!



# it can create links instead of copying the files
# it can move the files instead of copy or linking

New-Item -Path C:\LinkDir -ItemType SymbolicLink -Value F:\RealDir
