LOADBTM ON
BREAK ON
REM this needs to build a file for each day and then delete the first file
rem when it has reached 7 days later.loop
ECHO Backup-main Files v0.2
IFF %_pipe != 0 .OR. %_transient != 0 THEN & GOTO startcmd & ELSE & GOTO begin & ENDIFF

:startcmd
WINDOW MIN
GOTO begin

:begin
SETLOCAL
ALIAS cp = COPY /uvhzk
REM (a)dd -(s)olid -(r)ecovery(r)ecord -(d)ictionary 4096k -(t)imeStamp -co(m)pressionLevel -(y)es -(f)ull (p)ath (d)rive
ALIAS winace = *winace a -s -rr -r -fpd -d4096 -tl -m5 -y
ECHO Current backup path: %BACKUP%
ECHO.
CDD %BACKUP%\iSNAPSHOT
FREE
CD .\Editplus
cp "%SYSTEMDRIVE%\program files\editplus\*.ini"
cp "%SYSTEMDRIVE%\program files\editplus\*.usd"
cp "%SYSTEMDRIVE%\program files\editplus\template.*"
CD ..\Abiword
cp "%SYSTEMDRIVE%\program files\abisuite\templates\normal.awt"
CD ..\Winamp
cp "%SYSTEMDRIVE%\audio\play\winamp\winamp.ini"
cp "%SYSTEMDRIVE%\audio\play\winamp\winamp.q1"
cp "%SYSTEMDRIVE%\audio\play\winamp\winamp.q2"
CD ..\Web_Server
cp "%SYSTEMDRIVE%\windows\php.ini" .
cp "%SYSTEMDRIVE%\usr\httpd\apache2\conf\httpd.conf"
cp "c:\my.cnf" .
cp "%SYSTEMDRIVE%\windows\my.ini" .
CD ..\soulseek
cp "%SYSTEMDRIVE%\usr\bin\slsk\soulseek\*.cfg"
cd ..
cp "%SYSTEMDRIVE%\program files\hydrairc\*.xml"
MOVE %BACKUP%\LAST\documents.ace;ql_folders.ace;org.ace;playlists.ace;script.ace %BACKUP%\PENULT
MOVE %BACKUP%\CURRENT\documents.ace;ql_folders.ace;org.ace;playlists.ace;script.ace %BACKUP%\LAST
CDD %BACKUP%\CURRENT
REM includes xconf (4nt stuff)
ECHO compressing scripts
winace script.ace i:\script
ECHO compressing playlists
winace playlists.ace "h:\audio\music\playlists"
ECHO compressing organistational
winace org.ace f:\org
compressing quicklanuch_folders
winace "ql_folders.ace" "%SYSTEMDRIVE%\documents and settings\ql_folders"
compressing documents
winace documents.ace i:\documents
IFF %_pipe != 0 .OR. %_transient != 0 THEN & GOTO endcmd & ELSE & GOTO finish & ENDIFF
ENDLOCAL

:endcmd
EXIT

:finish
ECHO.
ECHO Finished backup of:
ECHO (quicklanuch, organisation,playlists,system scripts,tracklisting)
ECHO at %time% on %date%
CDD %BACKUP%
DIR
FREE
ECHO.
QUIT