LOADBTM ON
BREAK ON
REM edir (v0.2) mbishop 2003

set drivetype="%@FSTYPE[%_DISK:]"

IF "%_CWD" == "I:\res\mirror\www" goto fatdir
IF "%@SUBSTR[%_CWD,0,10]" == "c:\windows" goto lfndir
IF "%@SUBSTR[%_CWD,0,16]" == "c:\program files" GOTO lfndir

IF "%_DISK" == "z" GOTO lfndir
IF "%_DISK" == "y" GOTO lfndir
IF "%_DISK" == "m" GOTO lfndir
IF "%_DISK" == "i" GOTO lfndir

IF /I EXIST .\descript.ion GOTO fatdir


IF %drivetype% == "NTFS" GOTO lfndir
IF %drivetype% == "FAT" GOTO fatdir
IF %drivetype% == "FAT32" GOTO fatdir
GOTO end
:fatdir
DIR /hpz %1 %2 %3 %4 %5 %6 %7 %8 %9
GOTO end
:lfndir
DIR /hnp %1 %2 %3 %4 %5 %6 %7 %8 %9
GOTO end
:end
ECHO.
QUIT