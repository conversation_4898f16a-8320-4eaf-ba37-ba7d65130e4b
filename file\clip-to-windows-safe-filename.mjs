import { writeFileSync } from 'node:fs'
import clipboard from 'clipboardy'

function toWindowsSafeFilename(input) {
    // Replace invalid characters with an underscore
    return input.replace(/[<>:"/\\|?*\x00-\x1F]/g, '_').slice(0, 255).trim()
}

async function main() {
    try {
        // Get the clipboard content
        const clipboardContent = await clipboard.read()

        if (!clipboardContent) {
            console.error('Clipboard is empty.')
            return
        }

        // Convert to a safe filename
        const safeFilename = toWindowsSafeFilename(clipboardContent)

        // Copy the safe filename back to the clipboard
        await clipboard.write(safeFilename)

        console.log(`Safe filename copied to clipboard: ${safeFilename}`)
    } catch (error) {
        console.error('Error:', error.message)
    }
}

main()
