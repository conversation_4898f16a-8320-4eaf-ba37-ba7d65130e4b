@echo off
REM mklink /J 

REM I:\explore\backup\ver
REM I:\e\b\ver

REM ls -al /{c,d,e,f,h,i,j,m,o,p,s,t,u,w,x}/b/ver

REM TODO GOSUB type thing


mklink /J I:\e I:\explore
mkdir I:\e\backup
mklink /J I:\e\b I:\explore\backup

REM Setup drives mappings for sync on another computer
mkdir I:\e\dr
mklink /j I:\e\dr

mklink /j I:\e\dr\c c:\
mklink /j I:\e\dr\d d:\
mklink /j I:\e\dr\e e:\
mklink /j I:\e\dr\f f:\
mklink /j I:\e\dr\g g:\
mklink /j I:\e\dr\h h:\
mklink /j I:\e\dr\i i:\
mklink /j I:\e\dr\j j:\
mklink /j I:\e\dr\k k:\
mklink /j I:\e\dr\m m:\
mklink /j I:\e\dr\o o:\
mklink /j I:\e\dr\p p:\
mklink /j I:\e\dr\r r:\
mklink /j I:\e\dr\t t:\
mklink /j I:\e\dr\s s:\
mklink /j I:\e\dr\u u:\
mklink /j I:\e\dr\w w:\
mklink /j I:\e\dr\x x:\


REM Setup audio dirs
mkdir I:\e\au
mklink /j I:\e\audio I:\e\au
mkdir I:\e\au\s

mklink /j I:\e\au\s\d\e E:\au\s
mklink /j I:\e\au\s\d\u U:\au\s


REM --- SAMPLES
mklink /j I:\e\au\samples I:\e\au\s

REM FREE TO USE SOUNDS
mkdir I:\e\au\s\p\freetousesounds
mklink /j I:\e\au\s\p\freetousesounds\e E:\audio\samples\___purchased\freetousesoundscom
mklink /j I:\e\au\s\p\freetousesounds\j j:\audio\samples\___purchased\freetousesoundscom
mklink /j I:\e\au\s\p\freetousesounds\h h:\audio\samples\___purchased\freetousesoundscom
mklink /j I:\e\au\s\p\freetousesounds\k K:\audio\samples\___purchased\freetousesoundscom
mklink /j I:\e\au\s\p\freetousesounds\p P:\audio\samples\___purchased\freetousesoundscom
mklink /j I:\e\au\s\p\freetousesounds\r R:\audio\samples\___purchased\freetousesoundscom
mklink /j I:\e\au\s\p\freetousesounds\s S:\audio\samples\___purchased\freetousesoundscom
mklink /j I:\e\au\s\p\freetousesounds\u U:\audio\samples\___purchased\freetousesoundscom
mklink /j I:\e\au\s\p\freetousesounds\w W:\audio\samples\___purchased\freetousesoundscom




REM Setup version dirs
MKDIR I:\e\b\ver

IF EXIST C:\b\ver (
	mklink /J I:\e\b\ver\c C:\b\ver
)
IF EXIST D:\b\ver (
	mklink /J I:\e\b\ver\d D:\b\ver
)
IF EXIST E:\b\ver (
	mklink /J I:\e\b\ver\e E:\b\ver
)
IF EXIST F:\b\ver (
	mklink /J I:\e\b\ver\f F:\b\ver
)
IF EXIST H:\b\ver (
	mklink /J I:\e\b\ver\h H:\b\ver
)
IF EXIST I:\b\ver (
	mklink /J I:\e\b\ver\i I:\b\ver
)
IF EXIST J:\b\ver (
	mklink /J I:\e\b\ver\j J:\b\ver
)
IF EXIST M:\b\ver (
	mklink /J I:\e\b\ver\m M:\b\ver
)
IF EXIST O:\b\ver (
	mklink /J I:\e\b\ver\o O:\b\ver
)
IF EXIST P:\b\ver (
	mklink /J I:\e\b\ver\p P:\b\ver
)
IF EXIST S:\b\ver (
	mklink /J I:\e\b\ver\s S:\b\ver
)
IF EXIST T:\b\ver (
	mklink /J I:\e\b\ver\t T:\b\ver
)
IF EXIST U:\b\ver (
	mklink /J I:\e\b\ver\u U:\b\ver
)
IF EXIST W:\b\ver (
	mklink /J I:\e\b\ver\w W:\b\ver
)
IF EXIST X:\b\ver (
	mklink /J I:\e\b\ver\x X:\b\ver
)

REM SETUP O:\e\b\b b dirs
MKDIR I:\e\b\b

IF EXIST C:\b (
	mklink /J I:\e\b\b\c C:\b
)
IF EXIST D:\b (
	mklink /J I:\e\b\b\d D:\b
)
IF EXIST E:\b (
	mklink /J I:\e\b\b\e E:\b
)
IF EXIST F:\b (
	mklink /J I:\e\b\b\f F:\b
)
IF EXIST H:\b (
	mklink /J I:\e\b\b\h H:\b
)
IF EXIST I:\b (
	mklink /J I:\e\b\b\i I:\b
)
IF EXIST J:\b (
	mklink /J I:\e\b\b\j J:\b
)
IF EXIST M:\b (
	mklink /J I:\e\b\b\m M:\b
)
IF EXIST O:\b (
	mklink /J I:\e\b\b\o O:\b
)
IF EXIST P:\b (
	mklink /J I:\e\b\b\p P:\b
)
IF EXIST S:\b (
	mklink /J I:\e\b\b\s S:\b
)
IF EXIST T:\b (
	mklink /J I:\e\b\b\t T:\b
)
IF EXIST U:\b (
	mklink /J I:\e\b\b\u U:\b
)
IF EXIST W:\b (
	mklink /J I:\e\b\b\w W:\b
)
IF EXIST X:\b (
	mklink /J I:\e\b\b\x X:\b
)

REM SETUP O:\e\b\b b\auto dirs
MKDIR I:\e\b\auto

IF EXIST C:\b\auto (
	mklink /J I:\e\b\auto\c C:\b\auto
)
IF EXIST D:\b\auto (
	mklink /J I:\e\b\auto\d D:\b\auto
)
IF EXIST E:\b\auto (
	mklink /J I:\e\b\auto\e E:\b\auto
)
IF EXIST F:\b\auto (
	mklink /J I:\e\b\auto\f F:\b\auto
)
IF EXIST H:\b\auto (
	mklink /J I:\e\b\auto\h H:\b\auto
)
IF EXIST I:\b\auto (
	mklink /J I:\e\b\auto\i I:\b\auto
)
IF EXIST J:\b\auto (
	mklink /J I:\e\b\auto\j J:\b\auto
)
IF EXIST M:\b\auto (
	mklink /J I:\e\b\auto\m M:\b\auto
)
IF EXIST O:\b\auto (
	mklink /J I:\e\b\auto\o O:\b\auto
)
IF EXIST P:\b\auto (
	mklink /J I:\e\b\auto\p P:\b\auto
)
IF EXIST S:\b\auto (
	mklink /J I:\e\b\auto\s S:\b\auto
)
IF EXIST T:\b\auto (
	mklink /J I:\e\b\auto\t T:\b\auto
)
IF EXIST U:\b\auto (
	mklink /J I:\e\b\auto\u U:\b\auto
)
IF EXIST W:\b\auto (
	mklink /J I:\e\b\auto\w W:\b\auto
)
IF EXIST X:\b\auto (
	mklink /J I:\e\b\auto\x X:\b\auto
)

MKDIR I:\e\scratch
mklink /J I:\e\s I:\e\scratch

mklink /J I:\e\s\h H:\scratch
mklink /J I:\e\s\p P:\scratch

MKDIR I:\e\t

mklink /j I:\e\tmp I:\e\t
mklink /J I:\e\t I:\e\tmp

mklink /j I:\e\t\c c:\t
mklink /j I:\e\t\d d:\t
mklink /j I:\e\t\e e:\t
mklink /j I:\e\t\f f:\t
mklink /J I:\e\t\h H:\t
mklink /J I:\e\t\j j:\t
mklink /J I:\e\t\k k:\t
mklink /J I:\e\t\o o:\t
mklink /J I:\e\t\m m:\t
mklink /J I:\e\t\r r:\t
mklink /J I:\e\t\s s:\t
mklink /J I:\e\t\t t:\t
mklink /J I:\e\t\x X:\t
mklink /J I:\e\t\u u:\t
mklink /J I:\e\t\w w:\t
