LOADBTM ON
BREAK ON
ECHO ARCPROT.BTM : Archive a prototype ver 0.2.
REM TODO Add detection of files in archive dir and if exists delete or increment a index appended to date.
IFF %_pipe != 0 .OR. %_transient != 0 THEN & GOTO startcmd & ELSE & GOTO begin & ENDIFF
:startcmd
WINDOW MIN
GOTO begin
:begin
SETLOCAL

REM (a)dd -(s)olid -(r)ecovery(r)ecord -(d)ictionary 4096k -(t)imeStamp -co(m)pressionLevel -(y)es -(f)ull (p)ath (d)rive
ALIAS winace = *winace a -s -rr -r -fpd -d4096 -tl -m5 -y
ECHO.
set arc=www_root-prototype(%_DAY%-%_MONTH%-%_YEAR%)
set ext=.ace
IF /I EXIST ..\archive\%arc%%ext% del ..\archive\%arc%%ext%	
winace ..\archive\%arc% *.*
ENDLOCAL
IFF %_pipe != 0 .OR. %_transient != 0 THEN & GOTO endcmd & ELSE & GOTO finish & ENDIFF
:endcmd
EXIT
:finish
ECHO.
DIR ..\archive
ECHO Finished archiving prototype at %time% on %date%
DIR
FREE
ECHO.
QUIT