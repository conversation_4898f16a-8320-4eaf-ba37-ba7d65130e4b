# get all recent flps recursively the have been modified in the last 7 days

$FLP_LOCATION = "S:\pool\au\d\fl"

Write-Host

$recent_flps = Get-ChildItem -Path $FLP_LOCATION -Recurse -Filter "*.flp" | Where-Object { 
    $_.LastWriteTime -gt (Get-Date).AddDays(-7) 
}

$possible_colours = @("Red", "Green", "Blue", "Yellow", "Cyan", "Magenta")

$i = 0
foreach ($flp in $recent_flps) {
    $colour = $possible_colours[$i % $possible_colours.Count]

    $i++

    Write-Host $flp.FullName -ForegroundColor $colour
}
