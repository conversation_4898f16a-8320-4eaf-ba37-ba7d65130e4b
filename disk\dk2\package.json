{"name": "disk2", "version": "1.0.0", "description": "map disk into sql store in git", "main": "disk2.mjs", "scripts": {"test": "npx tap test/test-disk2.mjs"}, "author": "<PERSON>", "license": "GPL-3.0-only", "dependencies": {"boxen": "^8.0.1", "chalk": "^5.4.1", "env-paths": "^3.0.0", "is-string": "^1.1.1", "make-dir": "^5.0.0", "ora": "^8.1.1", "os-name": "^6.0.0", "simple-git": "^3.27.0", "sqlite3": "^5.1.7"}, "devDependencies": {"tap": "^21.0.1"}}