/*
Expects a path to a directory, uses a glob to match all shortcut files.
Renames the shortcut filename to the target shortcut filename in a standard form:-
__[DRIVE_LETTER]_[Part1].._[Part2].lnk
*/

import pkg_json from './package.json' with { type: "json" }

import fs from 'node:fs/promises'
import {homedir,platform} from 'node:os'
import path from 'node:path'
import assert from 'node:assert'
import {parseArgs} from 'node:util'
import { fileURLToPath } from 'node:url'
import {performance} from 'node:perf_hooks'

import {default as fg} from 'fast-glob'
import {default as ws} from 'windows-shortcuts'
import { confirm } from '@inquirer/prompts'
import Configstore from 'configstore'

let work = {
    d:{
        // mode == include
        include: [
        ],
        jobs: [],
        shortcuts_found: [],
        failed: [],
        args: {},
        date_started: Date.now(),
        date_ended: Date.now()
    },
    f:{},
    p:{
        sep: path.sep,
        GL<PERSON><PERSON>_ALL_LNK: '**/*.lnk',
        GLOB_ROOT_LNK: '/*.lnk',
        GLOB_INCLUDE: '*/{a,b,dd}/**/*.lnk'
    },
}

work.d.os = platform()
work.p.HOME_DIR = homedir()
work.d.SCRIPT_URL = import.meta.url
console.info('script url: ', work.d.SCRIPT_URL)
work.d.SCRIPT_HOME = fileURLToPath(import.meta.url)
console.info('script home: ', work.d.SCRIPT_HOME)
//work.d.TEST_ZIP_HOME = path.resolve(work.d.SCRIPT_HOME + path.sep + 'test-tree.zip')

work.f.IS_WIN = work.d.os === "win32"
work.f.IS_NIX = !work.f.IS_WIN
if (work.f.IS_NIX) {
    throw "Cannot run on none Windows OS"
}

// change path sep to other platform or correct way around
const _normalizePath = filePath => filePath
    .replace('\\\\','\\')
    .split(path.sep === '\\' ? '/' : '\\')
    .join(path.sep)

// Replace invalid characters with an hyphen, for windows os and standard on all
const _toSafeFileName = input => {
    return input.replace(/[<>:"/\\|?*\x00-\x1F]/g, '-').slice(0, 255).trim()
}

// -----
work.f.TESTING_DRYRUN_FORCE = !0
work.d.ID = pkg_json.name
work.d.FS_FRIENDLY_ID = 'lt--rls' // link tools - rename links std
work.d.argv = process.argv
work.d.ARG_DEF = {
    confirm: {
        desc: 'Each file is confirmed',
        type: 'boolean',
        short: 'c',
        default: false
    },
    backup: {
        desc:'Make a copy of the files before they are changed',
        type: 'boolean',
        short: 'b',
        default: false
    },
    config_clear: {
        desc: 'Clears the config store',
        type: 'boolean',
        default: false  
    },
    config_path: {
        desc: 'Shows the config path',    
        type: 'boolean',
        default: false
    },
    config_key: {
        desc: 'Sets a config key',
        type: 'string'
    },
    config_value: {
        desc: 'Sets a config value',
        type: 'string'
    },
    debug: {
        desc: 'Debugging mode',
        type: 'boolean',
        short: 'e',
        default: false
    },
    deep: {
        desc: 'Depth of the scan',
        type: 'string',
        default: '1'
    },
    dryrun: {
        desc: 'Does not perform file system changes',
        type: 'boolean',
        short: 'd',
        default: work.f.TESTING_DRYRUN_FORCE
    },
    failed: {
        desc: 'Move failed files to a failed location',
        type: 'boolean',
        default: false   
    },
    // TODO: if failed, ask user for new path... ????
    help: {
        desc: 'Shows this help',
        type: 'boolean',
        short: 'h',
        default: false
    },
    include: {
        desc: 'Include a glob pattern and don\'t use defaults',
        type: 'string',
        short: 'i',
        default: false
    },
    open: {
        desc: 'Opens the report via explorer.exe',
        type: 'boolean',
        default: false
    },
    recursive: {
        desc: 'Do not limit the depth and walk the tree for the glob',
        short: 'R',
        type: 'boolean',
        default: false
    },
    report: {
        desc: 'Writes a report to the HOME_LOCATION',
        type: 'boolean',
        default: false
    },
    // save_jobs: {
    //     short: 'S',
    //     type: 'boolean',
    //     default: false
    // },
    verbose: {
        desc: 'Verbose mode',
        short: 'v',
        type: 'boolean',
        default: false
    }

}
work.p.target_path = ''
if (work.d.argv.length) {
    work.p._parsed_args = parseArgs({ 
        args:work.d.argv, 
        options: work.d.ARG_DEF, 
        allowPositionals: true 
    })
    /**
        arguments:= 

        --backup make a copy of the files before they are changed... this will put them in a dir structure from config
        backup location

            ==> { from: pathLike, to: pathLike } FileCopy
    */
    work.d.args = work.p._parsed_args.values
    work.d.args.positionals = work.p._parsed_args.positionals
    // 1 == node command
    // 2 == script
    // 3 == first found positional argument
    // if the first found positional is a valid path qualifier....
    if(work.d.args.positionals[2]) {
        work.d.test_first_positional = work.d.args.positionals[2]
        work.p.target_path = path.resolve(
            _normalizePath(work.d.test_first_positional)
        )
        if (work.p.target_path) {
            console.info('target_path found as positional... ', work.p.target_path)
        }
        else {
            console.log('')
            console.warn('didn\'t find a positional path... using cwd?')
            if(!await confirm({message: 'continue?'})) {
                console.warn('user did not confirm cwd....')
                process.exit(1)
            } else {
                console.info('using cwd()')
            }
            console.log('')
            work.p.target_path = process.cwd()
        }
    }
    //work.p._parsed_args = null
} 

console.log('')
console.info('. ..: RENAME LINKS STD :.. .')
console.log('')
// TODO: update to count object members
if (work.d.args.length===0) {console.info('no extra args supplied...')}
if (work.f.TESTING_DRYRUN_FORCE) args.f.dryrun = true
    
const CONFIG = new Configstore(work.d.FS_FRIENDLY_ID)
if (work.d.args.config_path) {
    console.log('')
    console.info('CONFIG.path: ', CONFIG.path)
    console.log('')
    process.exit(0)
}
if (work.d.args.config_clear) {
    console.warn('config_clear will wipe the config .json store at\n'+CONFIG.path)
    if(await confirm({message: 'continue?'})) {
        CONFIG.clear()
        console.info('config cleared')
    } else {
        console.info('clear aborted')
    }
    process.exit(0)
}
if ((work.d.args.config_key && !work.d.args.config.value) || (work.d.args.config_value && !work.d.args.config_key)) {
    console.error(' to use config_value or config_key you must supply both together...')
    process.exit(1)
}
if (
    work.d.args.config_key && work.d.args.config_key.length && 
    work.d.args.config_value && work.d.args.config_value.length
) {
    console.info('setting config key: ', work.d.args.config_key)
    console.info('setting config value: ', work.d.args.config_value)
    CONFIG.set(work.d.args.config_key, work.d.args.config_value)
    console.log('')
    console.info(`configstore.get(${work.d.args.config.key}) says: `, CONFIG.get(work.d.args.config_key))
    process.exit(0)
}

if (work.d.args.help) {
    // if the first positional argument is a nomenclature of a help switch ... 
    let arg_one_test = work.d.args.positionals[2].toLowerCase()
    if (work.d.args.debug) console.info('arg_one_test', arg_one_test)
    if (work.d.args.help || arg_one_test === 'help' || arg_one_test === '--help' || arg_one_test === '-h') {
        console.log('')
        console.dir(work.d.ARG_DEF)
        console.log('')
        process.exit(0)
    }
}

work.p.HOME_LOCATION = path.resolve(
    _normalizePath(`${work.p.HOME_DIR}\\appdata\\roaming\\${work.d.FS_FRIENDLY_ID}`)
)
try { 
    await fs.access(work.p.HOME_LOCATION) 
}
catch (err) {
    console.warn('creating home location')
    await fs.mkdir(work.p.HOME_LOCATION)
}

if (work.d.args.verbose) console.info('home location: ', work.p.HOME_LOCATION)
work.p.NORMALIZED_HOME_LOCATION = _normalizePath(work.p.HOME_LOCATION)
if (work.d.args.verbose) console.info('normalized home location: ', work.p.NORMALIZED_HOME_LOCATION)

// try to create in home location `var/failed/`
work.p.NORMALIZED_FAILED_LOCATION = _normalizePath(work.p.NORMALIZED_HOME_LOCATION+path.sep+'var'+path.sep+ 'failed')
work.p.FAILED_LOCATION = path.resolve(work.p.NORMALIZED_FAILED_LOCATION)
try {
    await fs.access(work.p.FAILED_LOCATION)
    if (work.d.args.verbose) console.info('failed location: ', work.p.FAILED_LOCATION)
} 
catch(err) {
    console.warn('creating failed location')
    await fs.mkdir(work.p.NORMALIZED_FAILED_LOCATION)
    let exists = false
    if (!(exists=await fs.access(work.p.FAILED_LOCATION))) {
        console.log('')
        console.error('failed location does not exist, cannot create dirs in: ', work.p.FAILED_LOCATION)
        console.log('')
        process.exit(1)
    }
}

// it should use a zip file...
work.p.BACKUP_LOCATION = null
if (work.d.args.backup) {
    if (work.d.args.backup_location) {
        console.info('using argument backup location')
        try { await fs.access(work.d.args.backup_location) }
        catch (err) {
            console.log('')
            console.error('backup location does not exist')
            console.log('')
            process.exit(1)
        }    
        work.p.BACKUP_LOCATION = work.d.args.backup_location 
    }
    if(!work.d.args.backup_location) {
        console.info('getting config backup location')
        work.p.config_backup_location = CONFIG.get('BACKUP_LOCATION')
        console.info('config backup location: ', work.p.config_backup_location)
        if (!work.p.config_backup_location) {
            console.warn(`using default backup location instead of custom, 
                which can be set in the config json or via --config`
            )
            work.p.BACKUP_LOCATION = work.p.HOME_LOCATION + '\\backup'
        }
    }

    work.p.BACKUP_LOCATION = _normalizePath(work.p.BACKUP_LOCATION)

    // create the backup dir if it doesn't exist
    work.f.has_backup_location = false
    try {
        work.f.has_backup_location = await fs.access(work.p.BACKUP_LOCATION)
    } 
    catch(err) {
        if (work.d.args.verbose) {console.info('creating backup location: ', work.p.BACKUP_LOCATION)}
        await fs.mkdir(work.p.BACKUP_LOCATION)
    }
    if (work.d.args.debug) {console.info('backup location exists: ', work.f.has_backup_location)}
}

work.p.JOBS_LOCATION = path.resolve(work.p.HOME_LOCATION + path.sep + 'jobs')

if (work.d.args.verbose) {console.info('rest of args: ', work.d.argv)}
work.d.date_today = Date.now()
work.d.date_last_run = CONFIG.get('last_run')
work.d.date_ago = new Date(work.d.date_today-work.d.date_last_run)
work.d.time_ago_days = work.d.date_ago.getDay()
work.d.time_ago_hours = new Date(work.d.date_today-work.d.date_last_run).getHours()
if (work.d.args.verbose) {
    console.info(
        'config last-run: ', CONFIG.get('last_run'), 
        ' which was: ', work.d.time_ago_days, ' days and ' , 
        work.d.time_ago_hours,' hours ago '
    )
}

work.p.resolved_target_path = path.resolve(_normalizePath(work.p.target_path))
console.info('resolved_target_path: ', work.p.resolved_target_path)
if (work.d.args.backup) {console.info('backup location: ', work.p.BACKUP_LOCATION)}

// TODO: list each arg nicely
if (work.d.args.recursive) {console.info('setting recursion: ', work.d.args.recursive)}
else {console.info('scanning one dir deep...')}

try { await fs.access(work.p.resolved_target_path) }
catch (err) {
    console.error('Target path does not exist')
    process.exit(1)
}

/* process each shortcut to the standard */
const RE_WINDOWS_ALL_POSSIBLE_SHORTCUTS = /^[^<>:"\\|?*\x00-\x1F]*$/
const RE_STD_SHORTCUT = new RegExp('^__(?<drive>[A-Z])((?<part>__*[\u0020\#\.-a-zA-Z0-9]+)*)\.lnk$','gdu')
const RE_STD_PASSING_TESTS = [
    '__X_pool_au_inst.lnk',
    '__W_pool_au_inst.lnk',
    '__J_dddd_au_dddd.lnk',
    '__J_dd_au__new.lnk',
    '__A_b_c_d_e.lnk'
]
const RE_STD_FAILING_TESTS = [
    //'__X_a__b_x.lnk'
]

work.d.assert_results = []
const check_std_shortcut = (str, check) => {
    RE_STD_SHORTCUT.compile()
    let re = RE_STD_SHORTCUT.exec(str)
    if (work.d.args.debug) {
        console.info('RE:')
        console.dir(re)
    }
    assert(check(re), `Result: ${re} for ${str} failed against ${RE_STD_SHORTCUT}`)
    work.d.assert_results.push(re)
    re = null
}

// check the regular expression works on system....
check_std_shortcut(RE_STD_PASSING_TESTS[0], re => re !== null)
check_std_shortcut(RE_STD_PASSING_TESTS[1], re => re !== null)
check_std_shortcut(RE_STD_PASSING_TESTS[2], re => re !== null)
check_std_shortcut(RE_STD_PASSING_TESTS[3], re => re !== null)
check_std_shortcut(RE_STD_PASSING_TESTS[4], re => re !== null)
//check_std_shortcut(RE_STD_FAILING_TESTS[0], re => re.groups === undefined && re['0'] === '')


// now create a runtime test environment
// try {
//     fs.mkdir(`$(SCRIPT_HOME)\\test-tree\\1`)
// } catch(err) {
// }

// unzip the files into zip test
// copy the test files to the test tree
work.p.recursive_glob_path = work.p.resolved_target_path+_normalizePath(work.p.GLOB_ALL_LNK)
work.p.root_glob_path = work.p.resolved_target_path+_normalizePath(work.p.GLOB_ROOT_LNK)
if (work.d.args.debug) {
    console.info('root_glob_path: ', work.p.root_glob_path)
    console.info('recursive_glob_path: ', work.p.recursive_glob_path)
}
// TODO : there is a bug with scanning in root of paths given 
work.p.glob_expression = work.d.args.recursive 
    ? fg.convertPathToPattern(work.p.recursive_glob_path)
    : fg.convertPathToPattern(work.p.root_glob_path)
work.d.FAST_GLOB_RECURSIVE = {}
work.d.FAST_GLOB_SHALLOW = {deep: ''+parseInt(work.d.args.deep)}
work.d.FAST_GLOB_OPTIONS = {
    onlyFiles: true, 
    stats: true, 
    throwErrorOnBrokenSymbolicLink: true, 
    ...(work.d.args.recursive)
        ? work.d.FAST_GLOB_RECURSIVE
        : work.d.FAST_GLOB_SHALLOW
}
work.d.shortcuts_found = await fg.glob(work.p.glob_expression, work.d.FAST_GLOB_OPTIONS)

console.log('')
if (work.d.args.debug) console.info('glob expression', work.p.glob_expression)
console.info(`shortcuts with .lnk found: ${work.d.shortcuts_found.length}`)

if (work.d.shortcuts_found.length === 0) {
    console.log('')
    console.warn('nothing to do, try a dir with shortcuts in it')
    console.log('')
    process.exit(1)
}

assert(typeof work.d.shortcuts_found === 'object')

work.d.counter = 0
for(const data of work.d.shortcuts_found) {
    console.log('')
    console.info('start counter: ', work.d.counter)

    let job = {
        flags: {
            has_failed: false
        }
    }

    job.path = data.path
    job.stats = data?.stats
    job.name = ''+data.name
    job.parent = path.resolve(job.path+path.sep+'..')
    if (work.d.args.debug) console.info('shortcut_parent: ', job.parent)
    console.info(`processing ${job.name} located at: ${job.path}`)
    job.target = {}
    
    // get the job.target
    await new Promise((resolve, reject) => { 
        ws.query(job.path, async (err, d) => {
            if(err !== null) {
                reject('problem with windows-job.::shortcut.exe')
            } else {
                job.target = d.target
                resolve(job.target)
            }
        }) 
    })
    .catch(err => {
        console.error(err, err.message)
        job.flags.has_failed = true
    })

    // check the job.target is valid
    try { 
        await fs.access(job.target) 
        console.info('shortcut target exists')
    }
    catch(err) {
        console.warn('shortcut ', job.name, ' is invalid target: ', job.target)
        job.flags.has_failed = true
    }
    
    // ---------------

    if (!job.flags.has_failed) {

        job.drive = job.target.substr(0,1)
        console.log('')
        console.info('shortcut_drive: ', job.drive)
        job.new_target = _normalizePath(job.target)
        job.new_target = job.new_target.substr(3, job.new_target.length).split(path.sep)

        if (work.d.args.verbose) console.info('pre shortcut_target: ', job.new_target)
        job.new_filename = `__${job.drive}`
        
        if (job.new_target.length && job.new_target[0]!=='') {
            for(let i=0;i<job.new_target.length;i++) {
                job.new_filename+='_'+job.new_target[i]
            }
        }
        job.new_filename+='.lnk'

        job.new_filename = job.parent + path.sep + job.new_filename
        job.normalized_path = _normalizePath(job.path)
        job.new_filename = _normalizePath(job.new_filename)
        console.info('job.new_filename: ', job.new_filename)

        // now check that the file doesn't already exist if its renamed
        job.flags.has_new_shortcut_filename_already = true
        try { 
            job.flags.has_new_shortcut_filename_already = await fs.access(job.new_filename) 
        }
        catch(err) { 
            job.flags.has_failed = true
            job.flags.has_new_shortcut_filename_already = false
        }
        console.info('job.new_filename exists: ', job.flags.has_new_shortcut_filename_already)

        if (!job.flags.has_failed) {

            // no need to change the shortcut
            //ws.edit(shortcut_path, {}, (err,d) => {})

            if (work.d.args.backup) {
                job.backup_filename_root = (path.parse(job.normalized_path)).root
                if (work.d.args.debug) console.info('job.backup_filename_root: ', job.backup_filename_root)
                job.backup_filename = path.resolve(
                    work.p.BACKUP_LOCATION + path.sep + work.d.date_today + path.sep + job.path.substring(3, job.path.length)
                )
                job.msg_backup = `BACKUP:: COPY from: ${job.normalized_path}\n to: ${job.backup_filename}`
                console.info(job.msg_backup)
            }

            job.msg_rename = `RENAME: ${job.normalized_path}  to: ${job.new_filename}` 

            job.flags.has_confirmed_rename = false
            job.flags.should_rename_file = false
            job.flags.user_aborted_rename = false

            if (work.d.args.confirm) {
                job.flags.should_rename_file = await confirm({
                    message: job.msg_rename
                })
                if (!job.flags.should_rename_file) {
                    job.flags.user_aborted_rename = true
                    console.warn('user aborted rename')
                    job.flags.has_failed = true
                }
                job.flags.has_confirmed_rename = true
            } else {
                console.info(job.msg_rename)
                job.flags.has_confirmed_rename = true
                job.flags.should_rename_file = true
            }

            console.info('should rename file: ', job.flags.should_rename_file)

            job.flags.did_backup = false
            if (!work.d.args.dryrun && job.flags.has_confirmed_rename) {
                if (work.d.args.backup && !job.flags.has_failed && job.flags.should_rename_file) {
                    try {
                        await fs.copyFile(job.normalized_path, job.backup_filename)
                    } 
                    catch(err) {
                        console.error(err.message)
                        job.flags.has_failed = true
                    } 
                    job.flags.did_backup = true
                    console.info('did_backup: ', job.flags.did_backup)
                }

                if (job.flags.should_rename_file && !job.flags.has_failed) {
                    try {
                        await fs.rename(job.normalized_path, job.new_filename)
                        console.info('renamed file')
                    } catch(err) {
                        console.error('failed to rename file')
                        console.error(err)
                        job.flags.was_not_able_to_rename = true
                        job.flags.has_failed = true
                    }
                }
    
            }
        } // !job.has_failed after new file name check
    } 

    if (job.flags.has_failed) {
        work.d.failed.push(job)
        console.log('')
        console.warn(job.name, ' FAILED')
        console.log('')
    }

    work.d.jobs.push(job)
    console.info('end counter: ', work.d.counter)
    work.d.counter++
}
console.log('')

// save to explicit path, or use the built in last-work
CONFIG.set('last_jobs', work)

//--if (work.d.args.save_jobs) {}

// all runs are now saved by default...
const job_data = JSON.stringify(work)
try {
    await fs.writeFile(work.p.JOBS_LOCATION+path.sep+`${work.d.FS_FRIENDLY_ID}-jobs-${work.d.date_today}.json`, job_data)
    console.info('job data written to: ', work.p.JOBS_LOCATION+path.sep+`${work.d.FS_FRIENDLY_ID}-jobs-${work.d.date_today}.json`)
} 
catch(err) {
    console.error(err.message)
} 
// //await fs.writeFile('')


if (work.d.failed.length) {
    CONFIG.set('last_failed', work.d.failed)
    console.warn('INVALID shortcuts:-')
    if(work.d.args.verbose) {
        console.dir(work.d.failed)
    }
    let picked = work.d.failed.map(e => {e.path, e.name, e.target})
    console.dir(picked)
}

console.log('')
console.info('counter reached: ', work.d.counter, ' shortcuts found was: ', work.d.shortcuts_found.length)
console.info('successful operations: ', work.d.counter-work.d.failed.length)

// if work.d.args.verbose write a report to the HOME_LOCATION
if(work.d.args.report && work.d.args.debug) {
    try {
        process.report.writeReport(work.p.HOME_LOCATION+path.sep+'reports'+path.sep+`${work.d.FS_FRIENDLY_ID}-report-${date_today}.txt`)
    }
    catch(err) {
        console.error('failed to write report')
        console.error(err)
    }
    console.info('report written to: ', work.p.HOME_LOCATION+path.sep+'reports'+path.sep+`${work.d.FS_FRIENDLY_ID}-report-${date_today}.txt`)
    if (args.open) {
        exec(`explorer.exe ${work.p.HOME_LOCATION+path.sep+'reports'+path.sep+work.d.FS_FRIENDLY_ID-report-date_today.txt}`)
    }
}

work.d.date_ended = Date.now()
console.info('took: ', work.d.date_ended-work.d.date_started, 'ms')
CONFIG.set('last_run', work.d.date_ended)

