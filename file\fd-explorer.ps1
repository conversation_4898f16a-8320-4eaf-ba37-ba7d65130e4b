[CmdletBinding(
    PositionalBinding=$True,
    DefaultParameterSetName="Free"
)]
param(
    [Parameter(Mandatory=$False, ValueFromRemainingArguments=$False)]
    # set to current working dir if not specified
    [string]$SEARCH_PATH= $Null,

    [Parameter(Mandatory=$False)]
    [string]$tmp= "E:\t\_TMP\fd-explorer\0",

    # allow any args to pass thru:-
    [Parameter(ValueFromRemainingArguments=$True)]
    [string[]]$SEARCH_ARGS

    #[Parameter(Mandatory=$False)]
    #[string[]]$SEARCH_ARGS#,
)

# fd -- limit to directories using: --type d
# $ fd-explorer.ps1 -g bmp --type d

# if the search path is not specified
if (-not $SEARCH_PATH) {
    $SEARCH_PATH = $PWD.Path
}

# if the search path exists
if (-not (Test-Path -Path $SEARCH_PATH)) {
    Write-Error "Search path does not exist: $SEARCH_PATH"
    exit 1
}

if ($tmp) {
    Write-Info "Creating temp path for: $tmp"
    New-Item -ItemType Directory -Path $tmp
}

# 1. Create a temporary folder for shortcuts
$temp_folder = $tmp -or (Join-Path $env:TEMP "FdSearchResults_$(Get-Date -Format 'yyyyMMddHHmmss')")

# 2. Create the temporary folder
New-Item -ItemType Directory -Path $temp_folder # | Out-Null

$count = 0

Write-Host
Write-Host "Search path: $SEARCH_PATH"
Write-Host

# 3. Run fd and create shortcuts for each result
# Ensure 'fd.exe' is in your PATH or provide the full path
fd $SEARCH_ARGS "-p" $SEARCH_PATH | ForEach-Object {

    # if there are results
    if (-not $_) {
        Write-Host "No results found for the search pattern: $SEARCH_ARGS"
        exit 0
    }

    # if the temporary folder does not exist
    if (-not (Test-Path -Path $temp_folder)) {
        Write-Error "Temporary folder does not exist: $temp_folder"
        exit 1
    }

    Write-Host
    Write-Host "item: $count"

    $target_path = $_
    $link_name = Join-Path $temp_folder ([System.IO.Path]::GetFileName($targetPath) + ".lnk")

    # Create a shortcut
    $WShell = New-Object -ComObject WScript.Shell
    $shortcut_object = $WShell.CreateShortcut($link_name)
    $shortcut_object.TargetPath = $target_path
    # Optional: Set working directory if needed for the target
    #$shortcut_object.WorkingDirectory = [System.IO.Path]::GetDirectoryName($targetPath)
    $shortcut_object.Save()

    Write-Host "Created shortcut for: $target_path"
    $count++
}

explorer.exe $temp_folder

# Read-Host "Press Enter to remove the temporary folder '$temp_folder'..."
# Remove-Item -Recurse -Force $temp_folder