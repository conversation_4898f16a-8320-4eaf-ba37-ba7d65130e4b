echo
cd /i/lib/local/<PERSON><PERSON><PERSON>/src
ls -al /j/dd/au/___vendor/uhhyou/last-vstplugins-src.zip
echo
#rm -v /j/dd/au/___vendor/Uh<PERSON><PERSON>/last-vstplugins-src.zip#
recycle-bin /j/dd/au/___vendor/Uhhy<PERSON>/last-vstplugins-src.zip
zip -9 -r -v /j/dd/au/___vendor/Uhhy<PERSON>/last-vstplugins-src.zip ./VSTPlugins/*
echo
ls -al /j/dd/au/___vendor/Uhhy<PERSON>/last-vstplugins-src.zip
echo
#rm -fR *
#git clone --recursive https://github.com/ryukau/VSTPlugins
cd VSTPlugins
git fetch --all --force
git pull --recurse-submodules --force
echo
ls -al /j/dd/au/___vendor/Uhhyou
echo
cd ..
