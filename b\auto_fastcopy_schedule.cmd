REM Files and Dirs to be copied on schedule as a basic backup

@ECHO OFF
SET BCK_DEST=I:\b\auto\proc\auto_fastcopy_schedule\d

SET BASH_PROFILE=%BCK_DEST%\.bash_profile
SET BASH_HISTORY=%BCK_DEST%\.bash_history
SET I_PROC_MIRROR=%BCK_DEST%\i\proc

SET VAR_LOG_DIR=I:\b\var\log\proc\log\auto_fastcopy_schedule

REM set the log file to todays date:-
SET LOG_FILE=%VAR_LOG_DIR%\%date:~-4,4%%date:~-7,2%%date:~-10,2%_%time:~0,2%%time:~3,2%%time:~6,2%.log

REM explanation of copy switches:-
REM /Y - Suppress prompt to confirm overwriting a file
REM /V - Verify the files
SET CP_FILE=COPY /Y /V

REM -------------------------------------------------------

ECHO.
ECHO Started: auto_fastcopy_schedule.cmd

MKDIR %VAR_LOG_DIR% > %LOG_FILE%
MKDIR %BCK_DEST% > %LOG_FILE%
MKDIR %I_PROC_MIRROR% > %LOG_FILE%

REM REM set a record when it last ran
REM ECHO. > "%VAR_LOG_DIR%\last_run.touch"

%CP_FILE% "C:\Users\<USER>\.bash_profile" %BASH_PROFILE%
DIR %BASH_PROFILE% > %LOG_FILE%

%CP_FILE% "C:\Users\<USER>\.bash_history" %BASH_HISTORY%
DIR %BASH_HISTORY% > %LOG_FILE%

REM -------------------------------------------------------
REM Explanation of switches:-

REM /E - Copy subdirectories, including empty ones.
REM /S - Copy subdirectories, but not empty ones.
REM /SL - Copy symbolic links (not the target of the link).
REM /COPYALL - Copy all file info (equivalent to /COPY:DATSOU).
REM /PURGE - Delete dest files/dirs that no longer exist in source.
REM /L - List only - don't copy, timestamp or delete anything.
REM /NP - No Progress indicator in console (cleaner log).
REM /TIMFIX - Fix file times on all files, even skipped files.
REM /SECFIX - Fix file security on all files, even skipped files.
REM /R:3 - Retry 3 times on failed copies.
REM /W:5 - Wait 5 seconds between retries.
REM /MT:8 - Use 8 threads for copying (adjust as needed).
REM /ETA - Show Estimated Time Remaining.
REM /LOG+ - Append to log file.
REM /XD - Exclude Directories matching given names/paths.
REM /V - Verbose output

REM Disabled
REM /TIMFIX /SECFIX ^

SET CP_ROBO_MIRROR=ROBOCOPY /V /E /S /SL /XD node_modules /PURGE /R:3 /W:5 /MT:8 /LOG+:"%VAR_LOG_DIR%\%date:~-4,4%%date:~-7,2%%date:~-10,2%_%time:~0,2%%time:~3,2%%time:~6,2%.log"

@REM REM make a copy of proc since it could be lost, using robocopy
@REM %CP_ROBO_MIRROR% "I:\proc" %I_PROC_MIRROR%

@REM REM size of backup:-
@REM DIR /S /-C %I_PROC_MIRROR%

ECHO.
ECHO Finished: auto_fastcopy_schedule.cmd
ECHO.