//!/usr/bin/env node
import * as fs from "node:fs/promises";
import sqlite3 from "sqlite3";
import express from "express";
import cytoscape from "cytoscape";

const app = express();
const PORT = 3000;
const DB_PATH = "disk_structure.db"; // Path to your SQLite database

// Serve static files (e.g., HTML, CSS, JS for Cytoscape frontend)
app.use(express.static("public"));

// API to fetch data from SQLite
app.get("/data", async (req, res) => {
    const db = new sqlite3.Database(DB_PATH, (err) => {
        if (err) {
            console.error("Error opening database:", err.message);
            res.status(500).send("Error opening database");
        }
    });

    db.serialize(() => {
        const query = `SELECT * FROM files`;
        db.all(query, [], (err, rows) => {
            if (err) {
                console.error("Error executing query:", err.message);
                res.status(500).send("Error executing query");
            } else {
                // Transform data into Cytoscape.js compatible format
                const elements = rows.map(row => {
                    return {
                        data: {
                            id: row.id.toString(),
                            label: row.path,
                            size: row.size,
                            isDirectory: !!row.isDirectory,
                            parent: row.parentId ? row.parentId.toString() : null
                        }
                    };
                });

                // Return data as JSON
                res.json(elements);
            }
        });
    });

    // Properly close the database connection after the query is executed
    db.close((err) => {
        if (err) {
            console.error("Error closing database:", err.message);
        }
    });
});

// Frontend HTML for visualization
app.get("/", (req, res) => {
    const html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Disk Structure Visualization</title>
        <script src="https://unpkg.com/cytoscape/dist/cytoscape.min.js"></script>
        <style>
            #cy {
                width: 100%;
                height: 100vh;
                border: 1px solid #ddd;
            }
        </style>
    </head>
    <body>
        <div id="cy"></div>
        <script>
            fetch("/data")
                .then(response => response.json())
                .then(data => {
                    const cy = cytoscape({
                        container: document.getElementById('cy'),
                        elements: data,
                        style: [
                            {
                                selector: 'node',
                                style: {
                                    'label': 'data(label)',
                                    'width': 'mapData(size, 0, 1024, 10, 50)',
                                    'height': 'mapData(size, 0, 1024, 10, 50)',
                                    'background-color': '#0074D9',
                                    'color': '#fff',
                                    'text-valign': 'center',
                                    'text-outline-width': 1,
                                    'text-outline-color': '#0074D9'
                                }
                            },
                            {
                                selector: 'edge',
                                style: {
                                    'width': 2,
                                    'line-color': '#ddd',
                                    'target-arrow-color': '#ddd',
                                    'target-arrow-shape': 'triangle'
                                }
                            }
                        ],
                        layout: {
                            name: 'breadthfirst',
                            directed: true,
                            spacingFactor: 1.5
                        }
                    });
                })
                .catch(error => console.error("Error loading data:", error));
        </script>
    </body>
    </html>`;

    res.send(html);
});

// Start the server
app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});
