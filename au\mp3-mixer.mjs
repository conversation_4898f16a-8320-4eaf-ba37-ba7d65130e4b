#!/usr/bin/env node

/**
 * MP3 Mixer Script
 * 
 * This script mixes parts of MP3 files in various ways:
 * - Mix random chunks from one MP3 into another
 * - Mix multiple MP3 files together into a new output file
 * - Preserves headers to ensure playability
 */

import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'
import { <PERSON><PERSON><PERSON> } from 'buffer'

// Constants
const MP3_HEADER_SIZE = 10240 // Using 10KB as a conservative estimate to skip MP3 headers
const MP3_BITRATE = 128000 / 8 // 128kbps in bytes per second (typical MP3)
const MAX_CHUNK_DURATION = 10 // Maximum duration for random chunks in seconds

/**
 * Generate random number between min and max (inclusive)
 */
function getRandomInt(min, max) {
    min = Math.ceil(min)
    max = Math.floor(max)
    return Math.floor(Math.random() * (max - min + 1) + min)
}

/**
 * Generate a set of random chunks for mixing
 * @param {number} fileSize - Size of the file in bytes
 * @param {number} headerSize - Size of the header to preserve
 * @param {number} maxChunkSize - Maximum size of a chunk in bytes
 * @param {number} numChunks - Number of chunks to generate
 * @returns {Array} Array of chunk positions and sizes as [startPos, length]
 */
function generateRandomChunks(fileSize, headerSize, maxChunkSize, numChunks) {
    const chunks = []
    const availableSize = fileSize - headerSize
    
    if (availableSize <= 0) {
        console.warn('File is too small for chunking')
        return chunks
    }
    
    for (let i = 0; i < numChunks; i++) {
        const maxSize = Math.min(maxChunkSize, availableSize)
        const chunkSize = getRandomInt(Math.floor(maxSize * 0.1), maxSize)
        const maxStartPos = fileSize - chunkSize
        
        // Ensure we don't start in the header
        const startPos = getRandomInt(headerSize, Math.max(headerSize, maxStartPos))
        
        chunks.push([startPos, chunkSize])
    }
    
    return chunks
}

/**
 * Mix parts of source MP3 file with target file
 * @param {string} sourcePath - Path to source MP3 file (header will be preserved)
 * @param {string} targetPath - Path to target file to mix in
 * @param {string} outputPath - Path to save the mixed file
 * @param {Object} options - Mixing options
 */
async function mixMP3Files(sourcePath, targetPath, outputPath, options = {}) {
    const {
        startPos = MP3_HEADER_SIZE,
        mixLength = 1048576,
        mixRatio = 0.5,
        randomChunks = false,
        numChunks = 5
    } = options
    
    try {
        // Validate mix ratio
        if (mixRatio < 0 || mixRatio > 1) {
            throw new Error('Mix ratio must be between 0 and 1')
        }

        // Read source file
        const sourceFile = await fs.open(sourcePath, 'r')
        const sourceStats = await fs.stat(sourcePath)
        
        // Read target file
        const targetFile = await fs.open(targetPath, 'r')
        const targetStats = await fs.stat(targetPath)
        
        // Create output file
        const outputFile = await fs.open(outputPath, 'w')
        
        // First, copy the entire source file to the output
        const totalBuffer = Buffer.alloc(sourceStats.size)
        await sourceFile.read(totalBuffer, 0, sourceStats.size, 0)
        await outputFile.write(totalBuffer, 0, sourceStats.size, 0)
        
        if (randomChunks) {
            // Calculate maximum chunk size based on MP3 bitrate and max duration
            const maxChunkSize = MP3_BITRATE * MAX_CHUNK_DURATION
            
            // Generate random chunks
            const chunks = generateRandomChunks(
                sourceStats.size, 
                MP3_HEADER_SIZE, 
                maxChunkSize,
                numChunks
            )
            
            console.log(`Mixing ${chunks.length} random chunks`)
            
            for (const [chunkStart, chunkLength] of chunks) {
                // Read chunk from target file (looping if needed)
                const targetBuffer = Buffer.alloc(chunkLength)
                const targetPos = chunkStart % targetStats.size
                
                // Handle possible wrapping in target file
                if (targetPos + chunkLength <= targetStats.size) {
                    await targetFile.read(targetBuffer, 0, chunkLength, targetPos)
                } else {
                    // Read what we can from the current position
                    const firstPartSize = targetStats.size - targetPos
                    await targetFile.read(targetBuffer, 0, firstPartSize, targetPos)
                    
                    // Read the rest from the beginning
                    const remainingSize = chunkLength - firstPartSize
                    await targetFile.read(targetBuffer, firstPartSize, remainingSize, 0)
                }
                
                // Get source chunk that will be partially replaced
                const sourceChunk = Buffer.alloc(chunkLength)
                await sourceFile.read(sourceChunk, 0, chunkLength, chunkStart)
                
                // Mix the buffers based on the mix ratio
                for (let i = 0; i < chunkLength; i++) {
                    sourceChunk[i] = Math.floor(sourceChunk[i] * (1 - mixRatio) + targetBuffer[i] * mixRatio)
                }
                
                // Write mixed chunk back to output file
                await outputFile.write(sourceChunk, 0, chunkLength, chunkStart)
                
                console.log(`Mixed chunk at position ${chunkStart}, length ${chunkLength}`)
            }
        } else {
            // Original mixing logic for a single continuous chunk
            const maxMixLength = Math.min(sourceStats.size - startPos, targetStats.size)
            const actualMixLength = Math.min(mixLength, maxMixLength)
            
            console.log(`Mixing ${actualMixLength} bytes starting at position ${startPos}`)
            
            const sourceBuffer = Buffer.alloc(actualMixLength)
            const targetBuffer = Buffer.alloc(actualMixLength)
            
            await sourceFile.read(sourceBuffer, 0, actualMixLength, startPos)
            await targetFile.read(targetBuffer, 0, actualMixLength, 0)
            
            // Mix the buffers based on the mix ratio
            for (let i = 0; i < actualMixLength; i++) {
                sourceBuffer[i] = Math.floor(sourceBuffer[i] * (1 - mixRatio) + targetBuffer[i] * mixRatio)
            }
            
            await outputFile.write(sourceBuffer, 0, actualMixLength, startPos)
        }
        
        // Close all files
        await sourceFile.close()
        await targetFile.close()
        await outputFile.close()
        
        console.log(`Mixed file saved to ${outputPath}`)
    } catch (error) {
        console.error('Error mixing files:', error)
    }
}

/**
 * Mix multiple MP3 files together
 * @param {Array} filePaths - Array of file paths to mix
 * @param {string} outputPath - Path to save the mixed file
 * @param {Object} options - Mixing options
 */
async function mixMultipleFiles(filePaths, outputPath, options = {}) {
    if (filePaths.length < 2) {
        throw new Error('At least two files are required for mixing')
    }
    
    const { mixRatio = 0.5, randomChunks = true, numChunks = 5 } = options
    
    try {
        // Use the first file as the base
        const baseFile = filePaths[0]
        console.log(`Using ${baseFile} as the base file`)
        
        // Copy base file to output
        await fs.copyFile(baseFile, outputPath)
        
        // Mix each subsequent file into the output
        for (let i = 1; i < filePaths.length; i++) {
            console.log(`Mixing in file ${i}: ${filePaths[i]}`)
            
            // For multiple files, we use a temporary file for each step
            const tempOutputPath = `${outputPath}.temp`
            
            // Mix current file with the output so far
            await mixMP3Files(outputPath, filePaths[i], tempOutputPath, {
                ...options,
                mixRatio: mixRatio / (filePaths.length - 1), // Distribute mix ratio
                randomChunks,
                numChunks: Math.ceil(numChunks / (filePaths.length - 1))
            })
            
            // Replace the output with the temp file
            await fs.unlink(outputPath)
            await fs.rename(tempOutputPath, outputPath)
        }
        
        console.log(`Successfully mixed ${filePaths.length} files into ${outputPath}`)
    } catch (error) {
        console.error('Error mixing multiple files:', error)
    }
}

/**
 * Main function to process command line arguments
 */
async function main() {
    const args = process.argv.slice(2)
    
    if (args.length < 2) {
        console.log('Usage:')
        console.log('  Single file mix: node mp3-mixer.mjs --single <sourceMP3> <targetFile> <outputFile> [options]')
        console.log('  Multiple file mix: node mp3-mixer.mjs --multi <outputFile> <file1> <file2> [file3...] [options]')
        console.log('\nOptions:')
        console.log('  --random         Use random chunks for mixing (default for --multi)')
        console.log('  --chunks <num>   Number of random chunks to mix (default: 5)')
        console.log('  --ratio <float>  Mix ratio between 0-1 (default: 0.5)')
        console.log('  --pos <num>      Starting position for non-random mix (default: 10240)')
        console.log('  --len <num>      Length for non-random mix (default: 1MB)')
        process.exit(1)
    }
    
    // Parse command and options
    const mode = args[0]
    
    // Default options
    const options = {
        startPos: MP3_HEADER_SIZE,
        mixLength: 1048576, // 1MB
        mixRatio: 0.5,
        randomChunks: false,
        numChunks: 5
    }
    
    // Parse options
    for (let i = 0; i < args.length; i++) {
        switch (args[i]) {
            case '--random':
                options.randomChunks = true
                break
            case '--chunks':
                options.numChunks = parseInt(args[++i])
                break
            case '--ratio':
                options.mixRatio = parseFloat(args[++i])
                break
            case '--pos':
                options.startPos = parseInt(args[++i])
                break
            case '--len':
                options.mixLength = parseInt(args[++i])
                break
        }
    }
    
    // Process according to mode
    if (mode === '--single') {
        if (args.length < 4) {
            console.log('For single file mix, provide: --single <sourceMP3> <targetFile> <outputFile>')
            process.exit(1)
        }
        
        const sourcePath = args[1]
        const targetPath = args[2]
        const outputPath = args[3]
        
        await mixMP3Files(sourcePath, targetPath, outputPath, options)
    } else if (mode === '--multi') {
        if (args.length < 4) {
            console.log('For multiple file mix, provide: --multi <outputFile> <file1> <file2> [file3...]')
            process.exit(1)
        }
        
        const outputPath = args[1]
        const filePaths = args.slice(2).filter(arg => !arg.startsWith('--') && 
                                                 !['--random', '--chunks', '--ratio', '--pos', '--len']
                                                  .includes(args[args.indexOf(arg) - 1]))
        
        // Default to random chunks for multiple files
        if (!args.includes('--random') && !args.includes('--pos')) {
            options.randomChunks = true
        }
        
        await mixMultipleFiles(filePaths, outputPath, options)
    } else {
        console.log('Unknown mode. Use --single or --multi')
        process.exit(1)
    }
}

// Execute if this is the main script
if (path.basename(fileURLToPath(import.meta.url)) === 'mp3-mixer.mjs') {
    main().catch(err => {
        console.error('Error:', err)
        process.exit(1)
    })
}

export { mixMP3Files, mixMultipleFiles, generateRandomChunks }