
# searches all plugin dirs and orders them into a big list - most recent last

$industry_plugins_dirs = @(
    "C:\Program Files\VstPlugins",
    "C:\Program Files (x86)\VstPlugins",
    "C:\Program Files (x86)\Common Files\VST2",
    "C:\Program Files (x86)\Common Files\VST3",
    "C:\Program Files\Common Files\VST3",
    "C:\Program Files\Common Files\VST2",
    "C:\Program Files\Common Files\VST",
    "C:\Program Files (x86)\Steinberg\VstPlugins",
    "C:\Program Files\Steinberg\VstPlugins",
    "C:\Program Files\Common Files\Steinberg\VST2"
)

$local_plugin_dirs = @(
    "F:\a\vstpluginsrepo",
    "D:\a\red\VstPlugins",
    "D:\a\red\VstPlugins (x86)",
    "D:\Program Files\Common Files\VST2",
    "D:\Program Files\VstPlugins"
)

# scan the dir looking for a common date that was the latest modified time
Function Scan-PluginDir {
    param (
        [string]$Path
    )
    $parent_dir_modified = (Get-Item -Path ((Get-Item -Path $Path).Directory)).LastWriteTime
    
}