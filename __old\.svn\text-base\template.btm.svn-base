REM Enable 4Dos BTM Mode
LOADBTM ON
BREAK ON
IFF /I "%1" == " " THEN 
	GOSUB ERROR "Nothing to do! Specify /hs or /h for quick help and /hd fo detailed help" 
	GOTO END

REM Main script logic goes here
ELSEIFF /I "%1" == "/h" THEN
	GOSUB HELP simple
ELSEIFF /I "%1" == "/hs" THEN  
	GOSUB HELP simple
ELSEIFF /I "%1" == "/hd" THEN
	GOSUB HELP detailed
ENDIFF

:ERROR [errorStr]
ECHO.
ECHO Error:
ECHO.
ECHO %errorStr
ECHO.
RETURN

:HELP [level]
ECHO.
ECHO ArcMp3-2-CD
ECHO.
ECHO Synopsis:
ECHO.
ECHO ISO9660 MP3 filename formatter. Reduce, capitalize, lowercase and uppercase
ECHO operations for filenames. Specify source, destination dir and a argument to 
ECHO select the operation.
ECHO If you specify /a then the script will prompt you for necessary arguments.
ECHO.
ECHO This script EXPECTS well formed MP3 filenames, so you must use a hyphen to
ECHO deliminate categories you choose in your filenames.
ECHO.
ECHO Usage:
ECHO.
ECHO arcmp3-2-cd.btm [source-dir] [destination-dir] [operation]
ECHO.
IF %level == "simple" THEN GOTO simple
IF %level == "detailed THEN GOTO detailed
RETURN
:simple
ECHO operations: 
ECHO (v) - remove vouls, (c)apitalise 1st word
ECHO (CA) same as c for every word, (l)owercase, (u)ppercase
GOTO end
RETURN
:detailed
ECHO The 3rd  argument is optional, if specified, it is case incensitive and will instruct the 
ECHO script to perform the corresponding operation. based on the following possibilities:
ECHO.
ECHO V	: Remove vouls, from filenames to reduce length.
ECHO.
ECHO C	: Capitalize 1st letter of the 1st word for each filename.
ECHO.
ECHO CA	: Capitalize 1st letter and each subsequent word of the mp3 filename. This
ECHO	   is delimeted by the presence of a hyphen.
ECHO.
ECHO L	: Convert filename to lowercase.
ECHO.
ECHO U	: Convert filename to uppercase.
ECHO.
RETURN

:end
ECHO.
ECHO MattBishop, 2002-Beta-v0.1, <EMAIL>
ECHO.
QUIT