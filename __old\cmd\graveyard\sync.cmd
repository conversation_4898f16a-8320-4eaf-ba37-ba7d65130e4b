set backup=i:\__backup
echo file sync...
echo this file sync several development things between musicxp and devxp
echo.
echo individual files...
xcopy /v "e:\program files\editplus2\project.ini" "f:\program files\editplus2\project.ini"
echo backup...
echo this is only a crude backup as it is not incremental and also will never delete anything, so its not a exact copy... and will mean these  backups will grow and grow
xcopy /esv j:\data\*.* %backup%\collections\dirs\data\.
xcopy /esv i:\projects\*.* j:\__backup\collections\dirs\projects\