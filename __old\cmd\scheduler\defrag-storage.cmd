REM uses contig from sysinternals really nice way of approaching this
REM i notice afterwards they run defrag.exe to clean up and compact files

ECHO welcome to the automated defrag script! hope you have a nice time
ECHO.
REM -q quiet, -v verbose, -a analyse defragmentation, -s recurse subdirectories
REM contig.exe -q -v -s

ECHO server
ECHO.
START /low "contig" contig.exe -s R:\projects\*.*
START /low "contig" contig.exe -s R:\data\*.*
START /low "contig" contig.exe -s L:\audio\music\*.*
START /low "contig" contig.exe -s R:\temp\*.*
ECHO.
