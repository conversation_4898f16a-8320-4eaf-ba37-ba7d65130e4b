/** RUN-DAW-V2 
 * Copyright <PERSON> Bishop 2025
 * -- by changing the links the DAW will find these copies or symlinks first
 * -- if you change a .wav to a .flac find it
 *  v4 make it change that to a wav again
 */
//['Bitwig Studio', 'FL Studio', '']
// 1p
import pkg_json from './package.json' with { type: "json" }

import assert from 'node:assert'
import cluster from 'node:cluster'
import crypto from 'node:crypto'
import {createReadStream, createWriteStream, watch as fs_watch } from 'node:fs'
import {homedir, platform} from 'node:os'
import { env } from 'node:process'
import fs from 'node:fs/promises'
import * as nodePath from 'node:path'
import { performance } from 'node:perf_hooks'
import {pipeline} from 'node:stream/promises'
import readline from 'node:readline/promises'
import repl from 'node:repl'
import { parseArgs } from 'node:util'
import { fileURLToPath } from 'node:url'
import zlib from 'node:zlib'

const { eventLoopUtilization }= performance

// 3p 
import {execa} from 'execa'
import {default as gitRev} from 'git-rev'
import {default as ws} from 'windows-shortcuts'
import chalk from 'chalk'
import ora from 'ora'
import fastglob from 'fast-glob'
import Configstore from 'configstore'
import * as nodeTar from 'tar'
import { table } from 'table'
import {default as isTar} from 'is-tar'
import { rimraf } from 'rimraf'
import {default as blessed} from 'blessed'
import isInteractive from 'is-interactive'
import { ulid, monotonicFactory } from 'ulid'

/// #### STD LIB
const t= ref => (typeof ref).toString().toLowerCase()
const arr_op = (k, collection=  [], j= x => x) => {
    switch(k) {
        case 'many':
            return length(collection) > j
        case 'reduce':
            const fn = j
            return (collection
                // make the series unique
                .reduce(
                    (acc, current, index, arr) => {
                        if (acc.length === 0 || acc[acc.length - 1] !== current) {
                            acc.push(fn(current))
                        }
                        return acc
                    },
                    []
                ))
        case 'sort':
            return collection.sort()
        case 'pick':
            return collection.filter(j)
        case 'unique':
            return collection.filter((value, index, self) => {
                return self.indexOf(value) === index
            })
        case 'as_weakmap':
            return new WeakMap(collection)
        case 'as_weakset':
            return new WeakSet(collection)
        default:
            return []
    }
}
const obj_op = (k, o, j, k) => {
    switch(k) {
        case 'is_plain_object':
            return (o && typeof o === 'object' && !Array.isArray(o))
		case 'length':
			return o.length ? o.length : Object.keys(collection).length
        case 'as_weakmap':
            return new WeakMap(o)
        case 'as_weakset':
            return new WeakSet(o)
        default: 
            return {}
    }
}
const time_op = (k, i,j,k) => {
    switch(k) {
        case 'ms':
            return (j - i) / 1000
        case 's':
            return (j - i) / 1000
        case 'm':
            return (j - i) / 1000 / 60
        case 'h':
            return (j - i) / 1000 / 60 / 60
        case 'wait':
            const wait_ms = i || 100
            return new Promise(resolve => setTimeout(resolve, wait_ms))
        // delayed runs an array of functions, forwards, with a gap of x
        // case 'delayed':
        //     const delayed_gap = i 
        //     const delayed_deferred= Promise.resolve
        //     let o= {
        //         start: () => fns.forEach(fn => {
        //             o.deferred.then(() => {
        //                 return new Promise(resolve => {
        //                     setTimeout(() => {
        //                         fn().then(resolve)
        //                     }, delayed_gap)
        //                 })
        //             })
        //         })
        //     }
        //     delayed_functions.deferred= delayed_deferred
        //     return o
        // case 'delayed_start':
        //     return (delayed_functions.start())
        case 'now':
            return Date.now()
        // TODO: they need to also return teh returnValue
        case 'timed':
            const timed_start= now()
            i()
            const timed_end= now()
            return timed_end - timed_start
        case 'timed-precise':
            const timed_precise_start= performance.now()
            i()
            const timed_precise_end= performance.now()
            return timed_precise_end - timed_precise_start
        default:
            return Date.now()
    }
}
/// #### MODULE MANIFEST
/// #### this.d.$FEAT are hardcoded to the build type - generated by the build
if (DEV_MODE) {
    process.on('uncaughtException', err => {
		console.log()
        console.error(chalk.yellow('----> Uncaught Exception:'), err)
		console.log()
    })
    process.on('unhandledRejection', (reason, promise) => {
		console.log()
        console.error(chalk.yellow('----> Unhandled Rejection at:'), promise, 'reason:', reason)
		console.log()
    })
}
// #### MODULE FILE UTILS
/// file
//const files= paths => Promise.all(paths.map(fix_path(file)))
//const dir= path => fs.readdir(path, {withFileTypes: true})
//const dirs= path => fs.readdir(path, {withFileTypes: true}).filter(is_dir)
// const glob_from_driveroot= file => path_driveroot(file) + ':\\**\\*' + extension(file)
const fs_op = async (op, i,j,k) => {
	const path = fs_op('norm', i)
    assert(path, 'path is required')
    assert(await exists(path), 'path does not exist')
	try {
		switch(op) {
			case 'exists':
			case 'access': 
				await fs.access(path)
				break
            /// replace [filename][hash] etc from defaults
            case 'replace':
                for (let k in j) {
                    path= path.replaceAll(k, j[k])
                }
                return path
            /// return the number of child items within the path
            case 'counts':
                const counted= await fg(path + '\\**\\*')
                return counted.length
			case 'link':
				const from_type= (await fs.stat(i)).isDirectory() ? 'dir' : 'file'
				try {
					await fs.access(j) && is_link(j)
					return
				} 
				catch (err) {
					if (await exists(j)) {
						await COMMANDS.clear(j)
					}
					await fs.symlink(i, j, from_type)
				}
                break
			case 'pid':
				const path_pid= (await fs.stat(path)).ino
				j.d.pids[path]=path_pid
				return path_pid
			case 'is_link':
				return (await fs.lstat(path)).isSymbolicLink()
			case 'is_file':
				return (await fs.stat(path)).isFile()
			case 'mkdir':
				await fs.mkdir(path, { recursive: false })
				break
			case 'mkdirp':
				await fs.mkdir(path, { recursive: true })
				break
			case 'touch':
                await fs.utimes(path, new Date(), new Date())
                break
			case 'filename_hash':
				return crypto
                    .createHash('md5').update(path).digest('hex')
                    .toString('utf8')
			case 'norm':
				nodePath.resolve(nodePath.normalize(path))
				break
			case 'attributes':
			case 'attribs':
				const attrib_stats= await fs.stat(path)
				const o = {
					size: attrib_stats.size,
					isDirectory: attrib_stats.isDirectory(),
					createdAt: attrib_stats.birthtimeMs,
					modifiedAt: attrib_stats.mtimeMs,
					accessedAt: attrib_stats.atimeMs,
					mode: attrib_stats.mode,
					uid: attrib_stats.uid,
					gid: attrib_stats.gid
				}
                return o
		    /// recursively get the size of all items or of the item for a path
			case 'disk_size':
                const  disk_size_found= await fg(path + '\\**\\*')
                return disk_size_found.reduce((acc, file) => acc + file.stats.size, 0)
			case 'is_dir':
                return (await fs.stat(path)).isDirectory()
			case 'write_if_different':
				const data = j
				try {
					const existing_buffer= await fs.readFile(path)
					const new_buffer= Buffer.from(data)
					if (!existing_buffer.equals(new_buffer)) {
						await fs.writeFile(path, data)
					}
				} 
				catch (err) { 
					await fs.writeFile(path, data) 
				}
				break
			/// hash for filepath or dirpath
			case 'hash':
				const hash_isfile= fs_op('is_file', path)
				const hash_isdir= fs_op('is_dir', path)
				if (hash_isfile) {
					const md5_hash= crypto.createHash('md5')
					const data= await fs.readFile(path)
					md5_hash.update(data)
					const hash= md5_hash.digest('hex')
					return hash.toString('utf8')
				}
				else if (hash_isdir) {
					const hash= crypto.createHash('md5')
					const files= await fs.readdir(path)
					for await (const file of files) {
						const file_path= nodePath.resolve(path, file)
						const stats= await fs.stat(file_path)
						if (stats.isFile()) {
							const data= await fs.readFile(file_path)
							hash.update(data)
						}
						else if (stats.isDirectory()) {
							hash.update(await hash(file_path))
						}
					}
					return hash.digest('hex').toString('utf8')
				}
				return false
			// TODO: using fast, slow options brotli, zip
			// if args.verify static test_tar(test_tar_path) {}
			// if args.verify static test_zip(test_zip_path) {}
			case 'zip':
			case 'unzip':
				const zlib_ac = new AbortController()
				const signal = zlib_ac.signal
			case 'zip':
				const zip_path = i
				const src_path = j
				await pipeline(
					fs.createReadStream(src_path),
					zlib.createGzip(),
					fs.createWriteStream(zip_path),
					{ signal },
				)
				break
			case 'unzip':
				const src_zip_path = i
				const unzip_path = j
				await pipeline(
					fs.createReadStream(src_zip_path),
					zlib.createGunzip(),
					fs.createWriteStream(unzip_path),
					{ signal },
				)
				break
            case 'tar':
            case 'untar':
                const tar_path = i
                const dest_path = j
			case 'tar':
				await pipeline(
					nodeTar.c(
						{
							z: false, // 'z' is alias for 'gzip' option
						},
						[tar_path]
					),
					createWriteStream(dest_path)
				)
				break
			case 'untar':
				nodeTar.x({f: tar_path, cwd: dest_path})
                return
				break
            case 'read':
                return readline.createInterface({
                    input: createReadStream(path),
                    crlfDelay: Infinity
                })
                break
            case 'root-of':
                return path.split(':')[0]
            case 'with-now':
                // return filename with Date.now() between path.extension
                const with_now_ext= extension(path)
                const name= path.split('.').shift()
                return name + '.' + now() + '.' + with_now_ext
            case 'fg':
                return await fastglob.glob(g,  {
                    onlyFiles: true, 
                    stats: true, 
                    throwErrorOnBrokenSymbolicLink: true
                })
            case 'extension':
                let es= Job.$$CONF.get('seen-extensions') || []
                const ext= path.split('.').pop()
                /// if extension has not been seen before store it
                if (!es.includes(ext)) {
                    es.push(ext)
                    j.d.not_seen_extensions.push(ext)
                    Job.$$CONF.set('seen-extensions', unique(es))
                }
                return ext
            case 'recent':
                const recent_set_size=j || 1
                let recent_Files = []
                // for a path all unique items and the last one
                return recent_Files
                break

			/// for a path gets every file and basic path mod stats, 
			/// this is used to compare changes from operations
			/// this uses the script file cache
            case 'snapshot':
                break
		}
    } 
    catch (err) {
        return false
    }
	return Promise.resolve()
}
// #### MODULE SCOPE GLOBAL UTILS
/// FOR ALL setd
	// TODO: a set function modification that allows passing an object that then only changes those keys {k,v,desc}
	// TODO: immutable -> can only set once per script invocation
//const stick= (k,v) => !$$DEF[k] ? Job.setd(k,v) : Job.getd(k)/// used everywhere

/// in a final version this would be part of a `scan` command
/// this is also a questionnaire for the user

// TODO: emit events so that a gui can work with it?

// ---------------------------- end totally static
/// ### DEBUG features
// TODO: use bit masks and flags that way her

// ----------------------------------------------------------------------
    /// the cache is used to speed up the scanning process
    /// it caches all known areas - allowed via config
	/// you can instead issue `flr2 cache path/to/dir` ==> add a unique location
	/// or `flr2 cache` ==> scan all known cacheable locations SLOW

		/// if $$DEF['dynamic_cache_builder']

        /// when a chunk is processed a reference to a file is got
		/// if the reference is a kNOWN[k].cacheable or kNOWN[k].locations[i].cacheable then::==>

			/// during chunk processing _fast_scan ==> gets the immediate siblings of the path reference
                /// for each DIR -> get the file counts
				///		work out the magnitude
					/// if small counts cache them and mark as cached

// ----------------------------------------------------------------------
/// LOGGING
//const v= (i=0)=> (0,args.verbose>=i,)
//const d= (i=0) => (0,args.debug>=i,)
/// these are used to actually log from outside
const data_op = (k, d) => {
    switch(k) {
        case 'json':
            return JSON.stringify(d, null, 2)
        case 'csv':
            let csv= ''
            for (let i=0; i<d.length; i++) {
                csv+= `${d[i].k},${d[i].v},${d[i].desc}\n`
            }
            return csv        
        case 'sql':
            let sql= ''
            for (let i=0; i<d.length; i++) {
                sql+= `INSERT INTO ${d[i].k} (v, desc) VALUES ('${d[i].v}', '${d[i].desc}');\n`
            }
            return sql
        case crc:
            return zlib.crc32(d)
    }
}
/*
const record_atomic= (k, v, now=now(), fn) => {
    // used fixed size memory
    const sab= new SharedArrayBuffer(1024)
    const ta= new Uint8Array(sab)
    // Atomics.wait(int32, 0, 0);
    //console.log(int32[0]); // 123

    //console.log(int32[0]); // 0;
    //Atomics.store(int32, 0, 123);
    //Atomics.notify(int32, 0, 1);
}
*/
const str_op = (k, i,j,k) => {
    switch(k) {
        case 'trim_se':
            return i.trimStart().trimEnd()
        case 'trim_in':
            return i.replaceAll(/\s+/g, '')
        case 'trun':
            let s = i
            const l = j || 90
            const sym = k || ''
            return s.length > l ? s.slice(0, +l) + sym : s
        case 'has_spaces':
            return i.match(/\s+/g)
        case 'has_min_length':
            return i.length > j
		case 'fix_trailing_numbers':
		    //const valid= ['3']
			//const invalid= ['8']
			//const ext= extension(i)
			const i_last_char= i.split('').pop()
			const i_second_last_char= i.split('').pop()
			if (i_last_char === '3' && i_second_last_char !== '3') return i
			if (i_last_char === '8') i= i.replace(/[\d]+$/,'')
			return i
    }
}

class Job {
    static /// gets a matcher object to cycle calls through for each type
/// it does all the work during a chunk cycle
    Matcher({T}) {
        let m = {
            started: now()
        }
        m.collection = () => Job.$$KNOWN[T]
        m.coll = m.collection()
        assert(m.coll, 'no collection for ' + T)
        m.type = T
        m.roots = () => m.coll.roots
        m.extensions = () => m.coll.ext
        m.locations = () => m.coll.locations
            // TODO: cache these results, to avoid duplicate parsing...
        m.is = path => m.coll.locations
            .some(l => path.includes(l)) && 
            m.coll.ext.some(ext => path.endsWith('.' + ext))
        // TODO: next get current files cached to speed up checks later on using cache
            /// output set
        m.out = new Set()
        m.identify = path => {
            if (m.is(path)) m.out.add(path)
        }
    // currently uses a quick detection of numbers and chars one or more
    // my system uses U:\a\s for example
    // TODO: add path.sep
        m.contains_path = s => (new RegExp('','').test(s))
        m.is_interesting = s => (new RegExp('[a-z0-9]+','gi').test(line))
        m.is_nonpathchars= s => (new RegExp('[^<>:"/|?*\\x00-\\x1F]+','gi').test(s))
        m.has_matching_root = s => m.roots().some(r => s.startsWith(r + ':\\'))
    // ------------------------------------------------------------------------
        m.ended= now()
        return m
    }

    // TODO: with .extension
    ///const is_path= path => ((new RegExp('(?<drive>[A-Z]):\\\\(?<path>[^<>:"/|?*\\x00-\\x1F]+)','gi').test(path)))
    //const match_paths= path => ([...path.matchAll(new RegExp('(?<drive>[A-Z]):\\\\(?<path>[^<>:"/|?*\\x00-\\x1F]+)','gi'))])
    // //const is_xml_open= line => (new RegExp('<[a-z]+>','gi')).test(line)
    // const flp= path => extension(path) === 'flp'
    // const fst= path => extension(path) === 'fst'
    // const wav= path => extension(path) === 'wav'
    // const flac= path => extension(path) === 'flac'
    // const midi = path => ($$DEF['kNOWN']['midi'].ext.some(ext => path.endsWith('.' + ext)))
    // const preset = path => ($$DEF['kNOWN']['presets'].ext.some(ext => path.endsWith('.' + ext)))
    // const mp3= path => extension(path) === 'mp3'

        // const match_samples= (path) => {}
        // const sample_extensions= () => (DEFAULTS['kNOWN'].samples.ext)
        // 	/// drives they are possibly stored on or linked to
        // const sample_drive_roots= () => (DEFAULTS['kNOWN'].samples.roots)
        // const is_sample_path= path => {
        //     return 
        //         sample_extensions().some(ext => path.endsWith('.' + ext)) &&
        //         sample_drive_roots().some(drive => path.startsWith(drive + ':\\'))
        // }
        // const sample= path => record('samples', path)
        // const samples= paths => record('samples')
        // //const midi_extensions= () => (['mid','midi'])
        // //const is_midi_path= path => {
        // //    return midi_extensions().some(ext => path.endsWith('.' + ext))
        // //}
        // //const midi= path => record('midi', path)
        // //const midis= paths => record('midi')
        // 	/// files related to plugins
        // const match_plugins= (path) => {}
        // const plugin_extensions= () => (DEFAULTS['kNOWN'].plugins.ext)
        // 	/// drives plugins are stored on or linked to
        // const plugin_drive_roots= () => (DEFAULTS['kNOWN'].plugins.roots)
        // const is_plugin_path= path => {
        //     return $$DEF['KNOWN'].plugins.locations.some(
        //         l => path.includes(l)
        //     ) &&
        //         plugin_extensions().some(ext => path.endsWith('.' + ext)) &&
        //         plugin_drive_root().some(drive => path.startsWith(drive + ':\\'))
        // }
        // const plugin= path => record('plugins', path)
        // const plugins= paths => record('plugins')
    // const arr= async () => Promise.resolve([])
    // const eachAsync= async(collection, fn) => (
    //     count(collection) && 
    //     await Promise.all(Object.keys(collection).map(fn)) || arr()
    // )
    // const eachSync= (collection, fn) => (
    //     count(collection) && Object.keys(collection).forEach(fn) || []
    // )
    //const find= (collection, fn) => (collection.find(fn))
    // for an object count its members, for an array length

    /* there is a problem in that we don't know if a chunk will end during and 
    cut off a useful piece of path information */
    static $$CONF= function() {}
    static CONF() {
        Job.$$CONF= new Configstore(Job.getd('NAME'), {})
    }
    static setd(k='no key',v='no val',desc='no desc') {
        Job.$$DEF[k]= {k,v,desc}
    }
    static getd(k) {
        return Job.$$DEF[k].v
    }
    process(i,j) {
        switch(i) {
            case 'mem':
                now = i
                return now 
                    ? this.d.mem.push({
                        now:now||Date.now(),
                        mem:process.memoryUsage().rss
                    })
                    : this.d.mem[this.d.mem.length-1].mem
            case 'elu':
                now = i
                return now
                    ? this.d.elu.push({
                        now:now||Date.now(),
                        elu:eventLoopUtilization()
                    })
                    : eventLoopUtilization(this.d.elu[this.d.elu.length-1].elu).utilization
            default:
                return 0
        }
    }
    static argdef(){
        return({
            src: {
                desc: 'The source audio file',
                example: [
                    '--src path/to/file'
                ],
                type: 'string'
            },
            /// SWITCHES
                backup: {
                    desc: 'backup the source file to zip store for flp and fst stored by path filename hash regardless, useful backup, it clobbers the last one',
                    type: 'boolean',
                    default: false
                },
                ram: {
                    desc: 'run FL from ram using untar to ram and symlink back',
                    example: ['-r','--ram'],
                    type: 'boolean',
                    default: true
                },
                clearRam: {
                    desc: 'clear the ram drive files',
                    type: 'boolean',
                    default: false
                },
            samples: {
                desc: 'output samples as json and file list',
                type: 'boolean',
                default: true
            },
            plugins: {
                desc: 'output plugins as json and file list',
                type: 'boolean',
                default: false
            },
            midi: {
                desc: 'output midi as json and file list, this could massively slow it down I would not use this',
                type: 'boolean',
                default: false
            },
            presets: {
                desc: '',
                type: 'boolean',
                default: false
            },
            copySamples: {
                desc: 'copy samples to file store',
                type: 'boolean',
                default: false
            },
            copyPlugins: {
                desc: 'copy plugins to file store versioned to today',
                type: 'boolean',
                default: false
            },
            copyMidi: {},
            copyPresets: {},
            run: {
                desc: 'run FL, default is to exit',
                type: 'boolean',
                default: false
            },
                debug: {
                    desc: 'debug level as int',
                    type: 'string',
                    short: 'd',
                    default: '1'
                },
                verbose: {
                    desc: 'verbose level as int',
                    type: 'string',
                    short: 'v',
                    default: '1'
                },
            high: {
                desc: 'start fl with high priority',
                type: 'boolean',
                default: true
            }
        })
    }
    static cmddef() {
        return({
            'scan': {
                desc: 'scan for project files, from the path search for .fst, .flp, group these into common sets deliminated by unique startsWith and with the most recent version only ie `foo_path_1.flp`, `foo_path_32.flp` ==> foo_path_32.flp. _32.flp is the most recent one',
                type: 'string',
            },
            'build-cache': {
                desc: 'cache the locations using globs for all files, if path supplied cache this also. This only happens via this command build-cache. Use the feature dymamic caching to do this every time it runs for a shortwhile',
                type: 'string',
            },
            'last': {
                desc: 'Show the last run details',
                example: [
                    'last existing',
                    '-l existing',
                    '--last existing',
                    'existing | missing | samples | plugins'
                ],
                type: 'string',
                short: 'l',
                default: 'missing'
            },
            'version': {
                desc: 'Show the version',
                example: [
                    '--version',
                    '-V'
                ],
                type: 'boolean',
                default: false
            },
        })
    }
    static featdef() {
        return {
            'log': {v:1, desc:'enable logging'},
            'log.gl-fn-all-params': {v:0, desc:'the logger will record shortname, longname into the .record.Record'},
            'debug.chunk.input': {
                v: DEV_MODE && 0,
                desc: 'chunk has input copy... big slowdown'
            },
            'debug.patch.configstore.set':{v:DEV_MODE && 1, desc:'patch configstore set method to record keys being set into configstore!'},
            'diff-same-src-file-changes': {
                v: DEVMODE && 1,
                desc: 'by default it will notice when you change the same file name that was run and you didnt make another new file so it will diff the changes to reduce work load'
            },
            'diff-src-file-series-changes': {
                v: 1,
                desc: 'if its in series [sequence]_NUM.flp, diff that'
            },
            'dev-mode': {
                v: 1,
                desc: 'dev mode'
            },
            'release-mode': {
                v: 1,
                desc: 'release mode'
            },
            'no-user-defaults': {
                v:1,
                desc: 'do not use user defaults'
            },
            'guard-only-repo-src-copy':{
                v:1, desc:'you can only run this from a .git including local copy so it can be updated'},
            'user-defaults-override-script-defaults':{
                v:0, desc:'if the user stores modifications they are used over the script defaults'},
            // enable feature, without it will not try to find any files
            // it uses sqlite to search faster, than any elaborate file system and json solution
            'cache': {
                v: 1, 
                desc: 'this will improve performance over time - by knowing what files exist'
            },
            /// default processing
            'samples': {v:1 && RELEASE_MODE},
            'plugins': {v:0},
            'midi': {v:0},
            'presets': {v:0},
            /// show 
            'show.new': {v: DEV_MODE || RELEASE_MODE},
            'show.found': {v: DEV_MODE || RELEASE_MODE},
            'show.not-found': {v:1},
            'show.created-paths': {v: DEV_MODE},
            'show.changed-paths': {v: DEV_MODE},
            'show.deleted-paths': {v: DEV_MODE},
            'show.cache-hit': {v: DEV_MODE || RELEASE_MODE},
            'show.cache-miss': {v: DEV_MODE || RELEASE_MODE},
        
        }
    }
    POSITIONALS(arg_result) {
        if(arg_result.positionals) {
            const positionals= arg_result.positionals
            const possible_command= positionals.slice(1)
                /// TODO: auto check the length of the subsequent positionals matches the matching argument
            // if (Job[possible_command]) {
                //Job[positionals[0]](...
            // }
        }
        return null
    }
    ARGS= () => {
        const {values}= parseArgs({
            options: Job.argdef(), 
            allowPositionals: false
        })
        this.d.$$ARGS= {...values}
    }
    CMDS= () => {
        const {positionals}= parseArgs({
            options: Job.cmddef(), 
            allowPositionals: true
        })
        this.d.$$ARGS.positionals = positionals
    }
    FEATURES= () => {
        this.d.$$FEAT= Job.featdef()
    }
    static $$DEF= {}
    static DEFAULTS() {
        let o= {}
        /// ### from build system
        o.BREAKING_MAJOR_VERSION= 2
        o.BUNDLED_FLVER_FLOAT= '24.2.2.4597'
        o.CACHE_VERSION= 1
        o.SCRIPT_NAME= 'flr2.mjs'
        o.VER= pkg_json.version.replace('.','-')
        o.FLVER_FLOAT= BUNDLED_FLVER_FLOAT,
        o.FLVER= BUNDLED_FLVER_FLOAT.replace('.','-'),
        o.OUT_FILELIST_EXT= 'filelist',
        o.JSON= 'json',
        o.DIFF= 'diff',
        o.RAW= 'raw',
        o.TXT= 'txt',
        o.ENCODING= 'utf8'
        o.expected_config_set_keys_only= [
            'last-run',
            'last-data',
            'last-dev-mode',
            'seen-config-keys',
            'seen-extensions',
            'last-defaults',
            'USER_DEFAULTS'
        ]
        Job.$$DEF= o
    }
    static $$LOG=new class $$LOG {
        static format(t, d) {
            d= d ? d : 0
            switch(t) {
                case 'ms':
                    return (d / 1000).toFixed(2) + ' (ms)'
                case 'bytes':
                    return (d / 1024).toFixed(2) + ' (kb)'
                case 'lines':
                    return (d / 1000).toFixed(2) + ' (lines)'
                case 'progress':
                    return (d / 100).toFixed(2) + ' (%)'
                case 'terminal-width':
                    d= d===0 ?process.stdout.columns : d
                    return (d / 100).toFixed(2) + ' (columns)'
                default:
                    return t,d
            }
        }
        static cr() {
            return $$LOG.lbr()
        }
        static br() {
            return (0,force||args.verbose,console.log(),1)
        }
        y(s) {
            return this.i('Y: '+chalk.green(s))
        }
        n(s) {
            return this.i('N: '+chalk.gray(s))
        }
        h= $$LOG.gl('i')
        i= $$LOG.gl('i')
        w= $$LOG.gl('w')
        e= $$LOG.gl('e')
        verbose(i=1,j) {
            return (i>=args.verbose && li(j))
        }
        debug(i=1,k) {
            return (i>=args.debug && li(j))
        }
        function_name() {
            const error= new arguments.callee.Error()
            const stack= error.stack.split("\n")
            // The second line in the stack trace usually contains the function name
            const functionName= stack[2].trim().split(" ")[1]
            // TODO: make this walk up the chain to grandparent
            return functionName
        }
        static LOPT= {
            k: 'i', prefix: 'FLR>', timing: 1,
            pre: 0, post: 0,        
        }
        static LMAP= {'i':'info','e':'error','w':'warn'}
        // custom logger to update UI
        log= undefined
        lkv(d) {return(d.map(({k,v}) => (
            chalk.blue(k), 
            chalk.green(v)
        )))}
        /// get a logger
        static gl(x= $$LOG.LOPT) {    
            const iss = t(x)==='string'
            const st = x?.k ? x.k : iss ? x :'i'
            if(iss) x= {...$$LOG['LOPT']}
            const lg = $$LOG['LMAP'][st]
            if(!$$LOG[st]) $$LOG[st] = (s) => {
                if (typeof s==='function') s= s()
                // if not browser - ref change break it
                if (x?.prefix) s= `${x.prefix}:`+s
                if (x?.timing) s= `${now()}:`+s
                s+' '
                x.pre && lbr()
                switch(st) {
                    // here i do this so if you somehow recorded console[method] usages, you would know we match
                    // or if you patched them
                    case 'i':
                    case 'w':
                    case 'e':
                        $$LOG.log || console[lg](s)
                        break
                    default:
                        $$LOG.log || console.log(s)
                        break
                }
                x.post && lbr()
                    /// the cord is d.s
                let o = Job.$$DEF['log-gl-fn-all-params'] 
                    ? {lg,st,s} 
                    : {s}
                record(st, o) // ==> j.record[k].push({now,d:{v}})
            }
            return $$LOG[st]
        }
    }
    static $$KNOWN= {
        windows: {
            activated: [
                'project',
                'samples',
                'plugins',
                'midi',
                'presets'
            ],
            /// each stanza at this level is a type to find
            project: {
                ext: ['flp', 'fst'],
                roots: ['c','s'],
                locations: [
                    {c:'Documents\\Image-Line\\FL Studio', cacheable: false},
                    {k:'pool\\au\\d\\fl', cacheable: false}
                ]
            },
            samples: {
                ext: ['wav','mp3','ogg','flac'],
                roots: ['c','d','e','f','i','j','h','k','m','p','r','s','u','w','x'],
                    // TODO: later on it will collate this from previous runs
                    /// we can do this via counts
                locations: [
                    {k:'a\\s\\f',cacheable: true},
                    {k:'a\\coll',cacheable: true},
                    {k:'a\\s\\coll',cacheable: false},
                        /// these files are huge ... dont even bother
                    {k:'a\\s\\nf',cacheable: false},
                    {k:'a\\s\\red',cacheable: true},
                ],
                other_roots: [
                    // TODO: FL: {}
                    // TODO: hardcode these but using C:\users\<USER>\Documents\Image-Line\
                    '%FLStudioFactoryData%',
                    '%FLStudioDownloadData%'
                ],
            },
            midi: {
                ext: ['mid','midi'],
                roots: ['x'],
                locations: [
                    'a\\midi'
                ]
            },
            plugins: {
                cacheable: true,
                ext: ['clap','dll','vst3'],
                roots: ['c','d','f','j','h','k','p','r','s'],
                locations: [
                    [ // vst standard locations
                        'C:\\Program Files\\VstPlugins',
                        'C:\\Program Files (x86)\\VstPlugins',
                        'C:\\Program Files (x86)\\Common Files\\VST2',
                        'C:\\Program Files (x86)\\Common Files\\VST3',
                        'C:\\Program Files\\Common Files\\VST3',
                        'C:\\Program Files\\Common Files\\VST2',
                        'C:\\Program Files\\Common Files\\VST',
                        'C:\\Program Files (x86)\\Steinberg\\VstPlugins',
                        'C:\\Program Files\\Steinberg\\VstPlugins',
                        'C:\\Program Files\\Common Files\\Steinberg\\VST2'
                    ],
                    [ // user locations
                        'F:\\a\\vstpluginsrepo',
                        'D:\\a\\red\\VstPlugins',
                        'D:\\a\\red\\VstPlugins (x86)',
                        'D:\\Program Files\\Common Files\\VST2',
                        'D:\\Program Files\\VstPlugins'
                    ]
                ]
            },
            /// not cached
            presets: {
                cacheable: false,
                ext: ['vst3preset','mtpreset','dspreset','dslibrary','dsbundle'],
                roots: ['c','w', 'u', 's', 'm', 'j', 'x'],
                locations: [
                    /// they usually are in Documents\ somewhere
                ]
            }
        },
        strings_exe: {
            cacheable: true,
            name: 'strings2.exe',
            path: 'lib\\strings2.exe',
            desc: 'strings2 executable'
        },
        ffmpeg_exe: {
            cacheable: true,
            name: 'ffmpeg.exe',
            path: 'lib\\ffmpeg.exe',
            desc: 'ffmpeg executable'
        }
    }
    /// truncate the string to l and concat sym
    types() {
        return (Object.keys(Job.$$KNOWN']))
    }
    static P= {}
    static PATHS() {
        let o = {
            // ### ROOOTS
            ARC_ROOT: {
                k: 'ARC_ROOT',
                v: 'E:\\b\\arc',
                desc: 'archive root location - backup of fl, flp, fst'
            },
            VAR_ROOT: {
                k: 'VAR_ROOT',
                v: 'I:\\var\\d',
                desc: 'variable data root location - needs to be fast since it will write a few files per file processed not recommended on hdd'
            },
            RD_ROOT: {
                k: 'RD_ROOT',
                v: 'B:\\t\\proc',
                desc: 'ram drive root location - needs to be setup prior to script via imDisk'
            },
        }
        o.VER= {
            k: 'VER',
            v: o('VER'),
            desc: 'fl-run version'
        }
        o.NAME={
            k: 'NAME',
            v: `dr-v-${Job.$def.BREAKING_MAJOR_VERSION}`,
            desc: 'fl-run name'
        }
        
        o.FL_CURRENT_DIR= {
            k: 'FL_CURRENT_DIR',
            v: `D:\\a\\red\\prod\\2024\\${Job.getd('FLVER_FLOAT')}`,
            desc: 'FL current version location DIRECTORY only'
        }
        o.FL_BIN_64= {
            k: 'BIN_64',
            v: `${o.FL_CURRENT_DIR.v}\\FL64.exe`,
            desc: 'path to FL binary absolute'
        }
        o.ARC= {
            k: 'ARC',
            v: `${o.ARC_ROOT.v}\\${Job.getd('NAME')}`,
            desc: 'archive data location',
            example: 'E:\\b\\arc\\fl-run-2'
        }
        /// archive store can be slower but starting fl will be slower
/// e is a hard disk used for backup
/// since its reading it will be faster, as it wont be recreated
/// the path is only changed when major version changes

        o.ARC_STORE= {
            k: 'ARC_STORE',
            v: `${o.ARC.v}\\S`,
            desc: 'archive store location'
        }
        /// create a zip of the src file @ fastest independent of fl-run version
/// E:\b\arc\fl-run-2\S\ZIP\[filename].zip
/// independent of fl version and fl-run version
        o.ZBK= {
            k: 'ZBK',
            v: `${o.ARC_STORE.v}\\ZIP\\[file]`,
            desc: 'zip backup of the current flp'
        }
/// FL RUN VARIABLE DATA used by script so should be faster
	/// used debug, and ??? to increase runtime data persistence
    // I:\var\d\fl-run-2\1-0-0\24-2-2-4597

        o.VAR_DATA= {
            k: 'VAR_DATA',
            v: `${o.VAR_ROOT.v}\\${Job.getd('NAME')}\\${Job.getd('VER')}\\${Job.getd('FLVER')}`,
            desc: 'variable data location'
        }
        o.VAR_DATA_STORE= {
            k: 'VAR_DATA_STORE',
            v: `${o.VAR_DATA.v}\\S`,
            desc: 'variable data store location'
        }
        o.VAR_CACHE_STORE= {
            k: 'VAR_CACHE_STORE',
            v: `${o.VAR_DATA.v}\\C\\${CACHE_VERSION}`,
            desc: 'variable cache store location'
        }
        /// for a job for this script run we can assume ->  
// [file] is substituted
// [filename_hash] is hash for the path itself
/// DEFAULTS ==>

/// store of dir hashes --> the file name shows [unix epoch][md5hash] with no extension, meaning at this time it was this hash
/// path has here is the dir or the file path

/// the file name with the last hash
/// I:\var\d\fl.run.2\24-2-2-4597\d\[filehash]\
	/// for this filename_hash - the current content hash ( root path segment or file )

        o.PATH_HASH= {
            k: 'PATH_HASH',
            v: `${o.VAR_DATA.v}\\d\\[path_hash]`,
            desc: 'global store of hashes for dir paths'
        }
            PATH_HASH_OUT_ROOT= {
                k: 'PATH_HASH_OUT_ROOT',
                v: `${o.PATH_HASH.v}\\[src-file-hash]`,
                desc: 'for a hash of a file this is the output root'
            }
        o.DIRHASH= {
            k: 'DIRHASH',
            v: `${o.PATH_HASH.v}\\[now][dirhash]`,
            desc: 'hash of dir or file path'
        }

        o.OUT_RAW_TXT= {
            k: 'OUT_RAW_TXT',
            v: `${o.PATH_HASH_OUT_ROOT.v}.s2.txt`,
            desc: 'raw output of strings2'
        }
        o.OUT_DIFF_TXT= {
            k: 'OUT_DIFF_TXT',
            v: `${o.PATH_HASH_OUT_ROOT.v}.s2-last-diff.txt`,
            desc: 'diff output of strings2'
        }
        o.OUT_JSON_SAMPLES= {
            k: 'OUT_JSON_SAMPLES',
            v: `${o.PATH_HASH_OUT_ROOT.v}.samples.json`,
            desc: 'json output of samples'
        }
        o.OUT_FILELIST_SAMPLES= {
            k: 'OUT_FILELIST_SAMPLES',
            v: `${o.PATH_HASH_OUT_ROOT.v}.samples.${Job.getd('OUT_FILELIST_EXT')}`,
            desc: 'filelist output of samples'
        }
        o.OUT_JSON_PLUGINS= {
            k: 'OUT_JSON_PLUGINS',
            v: `${o.PATH_HASH_OUT_ROOT.v}.plugins.json`,
            desc: 'json output of plugins'
        }
        o.OUT_FILELIST_PLUGINS= {
            k: 'OUT_FILELIST_PLUGINS',
            v: `${o.PATH_HASH_OUT_ROOT.v}.plugins.${Job.getd('OUT_FILELIST_EXT')}`,
            desc: 'filelist output of plugins'
        }
        o.OUT_JSON_MIDI= {
            k: 'OUT_JSON_MIDI',
            v: `${o.PATH_HASH_OUT_ROOT.v}.midi.json`,
        }
        o.OUT_FILELIST_MIDI= {
            k: 'OUT_FILELIST_MIDI',
            v: `${o.PATH_HASH_OUT_ROOT.v}.midi.${Job.getd('OUT_FILELIST_EXT')}`,
        }
        o.OUT_JSON_PRESETS= {
            k: 'OUT_JSON_PRESETS',
            v: `${o.PATH_HASH_OUT_ROOT.v}.presets.json`,
        }
        o.OUT_FILELIST_PRESETS= {
            k: 'OUT_FILELIST_PRESETS',
            v: `${o.PATH_HASH_OUT_ROOT.v}.presets.${Job.getd('OUT_FILELIST_EXT')}`,
        }   
/// path to links that the browser will find early so that we can stop the stupid searching
/// here e == explore run file link store
/// this assumes every file is a unique name
        o.LS= {
            k: 'LS',
            v: `${o.VAR_DATA.v}\\LS`,
            desc: 'location of symlinks to files'
        }
        o.HS= {
            k: 'HS',
            v: `${o.VAR_DATA.v}\\HS`,
            desc: 'location of hardlinks to files'
        }
        /// the location of the copied file store
/// this assumes every file is a unique name
        o.CFS= {
            k: 'CFS',
            v: `${o.VAR_DATA.v}\\CFS`,
            desc: 'location of copied files'
        }
        o.JOBS= {
            k: 'JOBS',
            v: `${o.VAR_DATA.v}\\JOBS`,
            desc: 'location of jobs'
        }
        o.RD_FL_CURRENT= {
            k: 'RD_FL_CURRENT',
            v: `${o.RD_ROOT.v}\\${Job.getd('NAME')}\\${Job.getd('FLVER')}`,
            desc: 'ram drive run location'
        }
        o.FLTAR= {
            k: 'FLTAR',
            v: `${o.ARC.v}\\fl-tar\\${Job.getd('FLVER')}.tar`,
            desc: 'tar archive location of current version'
        }
        Job.P= o
    }
	d= {
        $$ARGS: {
            // in repl mode you can change them live
        },
        $$FEAT:{

        },
        $$CACHE: {},
        $$REC: {
            // .samples | .plugins | .midi | .presets | ?
            // as chunks

            // .log
                // .i | .e | .w
        },
		encoding: '',
		elu: [],
		mem: [],
		report: {
			started: now(),
			ended: now(),
			took: now(),
			last_run: now(),
			size_of_files: 0,
			files_created_count: 0,
			files_created: [],
			total_new_paths: 0,
			number_of_chunks: 0,
			not_seen_extensions: [],
            sql: [

            ]
		}
	}
	f= {
		is_run_main: nodePath.basename(fileURLToPath(import.meta.url)) === FLR2_SCRIPT_NAME,
        is_interactve: isInteractive(),
		is_first_run: false,
        is_one: false,
        is_many: false,
		    /// rating of the paths found to existing how much was recovered
		// stat_recovery_rating_int: 0
	}
    // set arg(arg_obj) {
    //     this.j.$args[arg_obj.k]= arg_obj.v
    // }
	//args_script() {}
	/// stores in j.record as series with date stamp
	record(k, v) {return (!this.record[k]
			? this.record[k]= [{now:now(),d:{...v}}] 
			: this.record[k].push({now:now(),d:{...v}})
	)}
	recorded(k) {return this.record[k]}
    constructor() {
    
    }
	//static #runCommand() {
		// so you can import COMMANDS and run one without doing other stuff
	//}

	static async default_setup() {
        if ($$DEF['debug.patch.configstore.set']) {
            const oldSet= Job.$$CONF.set
            Job.$$CONF.set= (k,v) => {
                // if not in the set
                if (!Job.$$CONF.get('seen-config-keys').includes(k)) {
                    oldSet('seen-config-keys', [...Job.$$CONF.get('seen-config-keys'), k])
                }
                oldSet(k,v)
            }
        }

/// if you change something we need to detect this so that we know whether to setup again
        if (Job.$$CONF.has('USER_DEFAULTS')) {
            /// user defaults works by DEFAULTS['USER_DEFAULTS']['NAME'] ==> ''
            try{
                assert(is_plain_object($$DEF['USER_DEFAULTS']), 'USER_DEFAULTS is not a plain object')
            }
            catch (err) {
                le(err)
            }
            Job.setd('USER_DEFAULTS', Jov.$$CONF.get('USER_DEFAULTS'))
        }

		if (Job.getd('user-defaults-override-script-defaults')) {
            if (Job.getd('USER_DEFAULTS')) {
				Job.setd('ORIG_DEFAULT', Job.$$DEF)
				for (let k in Job.$$DEF['USER_DEFAULTS']) {
					Job.$$DEF[k]= Job.$$DEF['USER_DEFAULTS'][k]
				}
			}
		}
		/// set data and defaults

		this.d.src_file_hash= await hash(j.$args.src)
		assert(this.d.src_file_hash, 'src_file_hash failed')
				
		if (Job.getd('guard-only-repo-src-copy')) {
			this.d.git_hash= gitRev.short()
			assert(this.d.git_hash, 'not running from official repo')
		}
		
        this.d.config_store_path= this.d.$$CONF.path
		assert(this.d.config_store_path, 'no path to config store')

		j.f.is_first_run= Job.$$CONF.get('last-run') === undefined
		//    const has_tar_archive= (path= PATHS.TAR_ARCHIVE) => exists(path)
		
		if (!Job.$$CONF.has('seen-extensions')) {
            Job.$$CONF.set('seen-extensions', [])
        }
        if (Job.$$FEAT['cache']) {
            Job.set_cache_options()
        }

        Job.$$CONF.set('last-dev-mode', DEV_MODE)
		return Promise.resolve(j)
	}
	default_init() {
		if (Job.$$CONF.has('last-run')) this.d.last_run= Job.$$CONF.get('last-run')
		else j.f.is_first_run= true
		j.f.is_run_main= IS_MAIN

		Job.env()
        Job.CONF()
        Job.DEFAULTS()
        Job.PATHS()
        Job.POSITIONALS()
        Job.CMDS()
        // with:
        Job.$args()

		assert(this.d.$$ARGS, 'args is required')
		assert(this.d.$$ARGS.src, 'src is required')
		assert(fs_op('is_file'), j.$args.src, 'src is not a file')

		/// if not src comes from ['sources','...s2']
		this.d.$$ARGS.verbose= parseInt(this.d.$$ARGS.verbose)
		this.d.$$ARGS.debug= parseInt(this.d.$$ARGS.debug)

	}
    
    static async default_start(start_src_path) {
        lh('..:: START JOBS ::..')
        li(fms(timed(Job.default_setup())))
        li(fms(timed(Job.default_init())))
        //li(fms(timed(await Job.start(args.src))))
            //soon(() => (li(ms(timed(filter())))))
        end()
        // if repl
        // if the file changed since last run diff its output
            // const ac = new AbortController()
            // const signal = ac.signal
        
        // this will be a text diff from the last .txt
        // then change the read stream behaviour, within the readStream, seek a chunk of text that ends where white space begins. if its under the MIN_CHUNK_LENGTH get greedy

        // this then consumed by workers

        // TODO: if the user is impatient they can make all workers lower priority and then continue to loading FL

        /// run a duplex stream to read in the flp into strings2 and output it to a file
        // await pipeline(
        //         createReadStream(start_src_path),
        //         execa({lines: true})`${$$DEF['strings2_cmd']}`.duplex(),
        //         createWriteStream($$DEF['OUT_RAW_TXT']),
        //         async function* (source, { signal }) {
        //             // Work with strings rather than `Buffer`s.
        //             source.setEncoding('utf8')
        //             for await (const chunk of source) {
        //                 yield await process_chunk(chunk, { signal })
        //             }
        //         }
        //     )
        //     .then(() => {
        //         /// wait for the disk to catchup
        //             wait(50)
        //     })
        return Promise.resolve()
    }
    /// chunks of data of lines
    static chunkdef() {
        return ({
            type: '', // should be string
            length: 0, // should be int
            d: {

            }
            
        })
    }
    async process_chunk(d) {
        let o= Job.chunkdef()
        o.type= typed(d)
        o.length= length(d)
        if (Job.$$FEAT['debug.chunk.input']) o.d.input= d
        await eachAsync(o, (d,i,a) => {
            // for each line chunk get a Matcher
            Job.$$DEF['kNOWN'].activated.forEach(T => {
                const m= Matcher({T})
                if (m.is(d)) {
                    // add to the collection
                }
            })
        })
    }
    async ram() {
        if (args.ram) this.record(
            'ram_drive_fl_studio_hash', await hash($$DEF['RD_FL_CURRENT'])
        )
        // if not this.d.ram
        return Promise.resolve()
    }
    set_cache_options() {

        /// ### FLR CACHE
        Job.setd('cache_db', `${Job.getd('VAR_DATA')}\\flr-cache.db`, 'sqlite database location')
        Job.setd('cache_db_table', 'cache', 'sqlite database table name')
        Job.setd('cache_db_table_cols', [
            'id INTEGER PRIMARY KEY AUTOINCREMENT',
            'version INTEGER',
            'path TEXT',
            'root TEXT', // drive root
            'path_hash TEXT',
            'stat_hash TEXT',
            'count INTEGER',
            'count_hash TEXT',
            'size INTEGER',
            'isDirectory INTEGER',
            'createdAt INTEGER',
            'modifiedAt INTEGER',
            'accessedAt INTEGER',
            'mode INTEGER',
            'uid INTEGER',
            'gid INTEGER'
        ], 'cache sqlite database table columns')
        Job.setd('cache_schema', `CREATE TABLE IF NOT EXISTS metadata (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT UNIQUE,
            value TEXT
        )`)
/// used by main and the cache_worker cluster core

		/// this will mean missing files were not found and not a huge improvement will be made to daw load time, if you have many files consider sleeping whilst run scan...
		Job.setd('cache_maxseektime', 200000, ' after 200000 (ms) give searching the cache for files or trying to find them')

// this uses fast-glob to get the entries but it does it slowly or when idle
// some dirs might contain vast amounts of samples... 

		Job.setd('cache_entrychunksize', 250000, 'splits the cache dirs up so no one dir is huge, ie explorer when navigating')
        this.d.cache= {}
        this.d.cache.options= {
            enabled: Job.getd('feat.cache'),
            db: Job.getd('cache_db'),
            table: Job.getd('cache_db_table'),
            cols: Job.getd('cache_db_table_cols'),
            maxseektime: Job.getd('cache_maxseektime'),
            entrychunksize: Job.getd('cache_entrychunksize')
        }
    }
    static async cache_op(op,ov) {

    }
        
    static async cache(new_path) {
        /// check path no hashed before and compare current with then
        return Promise.resolve()
    }
/// using the cache find a filename
	static async cache_find(filename) {
	}
    // ----------------------------------------------------------------------------------
    // ----------------------------------------------------------------------------------
    /// show is 
/// last allows you to quickly see a type and its results
    show(type) {
        const src = this.d[type] || Job.$$CONF.get('last_data')
		// for the type get the found, missing and what was done but in minimal formatting
        switch(type) {
            case 'version':
                console.log(pkg_json.version)
                break
            case 'defaults':
                console.table($$DEF)
                break
            case 'features':
                console.table($$DEF['feat'])
                break
            case 'cache_options':
                return this.d.cache.options
            case 'commands':
                console.table(this.d.COMMANDS)
                break
            case 'encoding':
                this.d.encoding
                break
            case 'existing':
                break
            case 'missing':
                break
            case 'samples':
                break
            case 'plugins':
                break
            case 'changed':
                break
            case 'deleted':
                break
            case 'created':
                break
            case 'all':
                break
            default:
                this.Job.$$LOG.br()
                break
        }
    }
	// ---------------------------------------------------------------------------------
    // COMMANDS.clear()
    static async clear(path) {
        // if link unlink 
        if (await fs_op('is_link',path)) {
            await fs.unlink(path)
            return true
        }
        // if dir 
        if (await fs_op('is_dir',path)) {
            await fs.rm(path, { recursive: true, force: true })
			await fs.rm(path)
			assert(!exists(path), "the path still exists")
            return true
        }
        // if file
        if (await fs_op('is_file',path)) {
            await fs.rm(path)
			assert(!fs_op('exists',path), "the file still exists")
            return true
        }
        return false
    }
    report(k,v) {
        if (!this.d.report[k]) this.d.report[k]= []
        if (v) this.d.report[k].push(v)
    }
    end() {
        this.d.ended= now()
        this.d.took= this.d.ended - this.d.started
        mem()
        Job.$$CONF.set('last-run', now())
        Job.$$CONF.set('last_data', this.d)
        if(args.debug>2) this.show()
        h('..:: END ::..')
    }
    static WORKER_TYPE = {
        // handles repl and main thread
		// prisma, sqllite 
        main: {
			index: 0,
            limit: 1,
            desc: 'main thread'
		},
		// restore and update cache
        cacheWorker: {
			index: 1,
            limit: undefined,
            desc: 'cache worker'
		},
        // runs fastify and admin interface
        webserver: {
			index: 2,
            limit: 1,
            desc: 'web server'
		},
        // process chunks from main
        chunkWorker: {
            index: 3,
            // must be a path on another disk
            limit: 3,
            desc: 'chunk worker'
		},
    }
}

//const cmd_matcher= (ok,ov) => {}
// const repl= async () => {

//     allow set defaults or args
//     call cmd matcher

//     const repl= repl.start({
//         prompt: 'fl-run> ',
//         eval: (cmd, context, filename, callback) => {
//             callback(null, eval(cmd))
//         }
//     })
// }

export async function main() {
    assert(process.platform==='win32', 'This script only runs on Windows')
}

// --------------------------------------------------------------------------------------
// RUN DAW PROGRAM ITSELF
process.on('exit', code => {
    //ending()
})

/**
 *  TODO: moving to cpu threaded execution means we need to remove the j && j.d. concept and move to sqlite. However we probably can make v3 use Jacket. Thus removing myne
 */
if(IS_MAIN) {
    if(cluster.isPrimary) {

		/// use start with priority normal or if --high high
		const spawn_start= () => {
		}
		const is_running= (path) => {}

		const fl_pid= () => {}
		const fl_session= async (options= {ram: true}) => {
		}

        await main().catch(err => {
            console.error('Error:', err)
            process.exit(1)
        })
    }
}