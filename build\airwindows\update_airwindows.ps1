[CmdletBinding(
    PositionalBinding= $False
)]
param (
    [Parameter(Mandatory= $False)]
    [string]$WORK_DIR= $Null,

	[Parameter(Mandatory= $False)]
    [string]$PLUGS_DIR= $Null,

	[Parameter(Mandatory= $False)]
    [switch]$SYNC= $Null,

	[Parameter(Mandatory= $False)]
	[switch]$NO_AGE_CHECK= $False,

	[Parameter(Mandatory= $False)]
	[switch]$NO_CONSOLIDATED_DOWNLOAD= $False,

	[Parameter(Mandatory= $False)]
	[switch]$FORCE_ARCHIVE_CYCLE= $FALSE
)

#Remove-Item Alias:Curl

#### library code from this script
class SevenZipWrapper {
	static Compress($path, $destination) {
		# 7z a a.7z *.exe *.dll -m0=BCJ2 -m1=LZMA:d25 -m2=LZMA:d19 -m3=LZMA:d19 -mb0:1 -mb0s1:2 -mb0s2:3
		# adds *.exe and *.dll files to archive a.7z using BCJ2 filter, LZMA with 32 MB dictionary for main output stream (s0), and LZMA with 512 KB dictionary for s1 and s2 output streams of BCJ2.

		#cmd = "a"
			# -mx9 ultra compression
			# -m0=LZMA2
			# -md=196M 196mb dictionary

			# TODO: (matt): word size
		7zS.exe a -y -bb3 -m0=LZMA2 -mx9 -md=224M -mt=on $path $destination
	}
	static Extract($path, $destination) {
		7zS.exe e -y -bb3 $path -o $destination
	}

	# 7-Zip 22.01 ZS v1.5.5 R3 (x64) : Copyright (c) 1999-2022 Igor Pavlov, 2016-2023 Tino Reichardt : 2023-06-18

	# Scanning the drive for archives:
	# 1 file, 2685503 bytes (2623 KiB)

	# Testing archive: Zplane.Tonic.V1.1.0.7z
	# --
	# Path = Zplane.Tonic.V1.1.0.7z
	# Type = 7z
	# Physical Size = 2685503
	# Headers Size = 240
	# Method = LZMA2:24
	# Solid = +
	# Blocks = 1

	# Everything is Ok

	# Folders: 1
	# Files: 2
	# Size:       14762536
	# Compressed: 2685503

	static [boolean]Test($path) {
		$result = 7zS.exe  t -y $path
		Write-Host $result
		if ($result.Contains("Everything is Ok")) {
			return $True
		}
		# TODO: (matt): not sure what it says when invalid
		return $False
	}
}
#### end library code from this script

#### HANDLE ARGUMENTS 

$BUILD_SRC_LOCATION = "I:\lib\local\AirWindows"
$SRC_ARC_LOCATION = "E:\b\arc\lib\local\AirWindows"
$AW_ARC_DIR = "\airwindows.2"

if ($WORK_DIR -and (Test-Path -Path $WORK_DIR)) {
	$WORKING_DIR_LOCATION_item = (Get-Item -Path $WORK_DIR)
	$WORKING_DIR_LOCATION_fi = $WORKING_DIR_LOCATION_item.FileInfo
	if ($WORKING_DIR_LOCATION_fi.BaseName -eq "airwindows") {
		Write-Host "Trimming airwindows from arc location"
		$WORKING_DIR_LOCATION = Split-Path -Path $WORK_DIR -Parent
	} else {
		$WORKING_DIR_LOCATION = $WORK.TrimEnd("\")
	}

	if (-not (Test-Path -Path $WORK_DIR)) {
		New-Item -Path $WORK_DIR -ItemType Directory -Verbose
	}
} else {
	$WORKING_DIR_LOCATION = "J:\dd\au\fx"
}

if (-not (Test-Path -Path $WORKING_DIR_LOCATION)) {
	Write-Error "Working dir location $($WORKING_DIR_LOCATION) does not exist"
	New-Item -Path $WORKING_DIR_LOCATION -Name "airwindows" -ItemType Directory
} else {
	Write-Host "Working dir location $($WORKING_DIR_LOCATION) exists"
}

if ($PLUGS_DIR -and (Test-Path -Path $PLUGS_DIR)) {

	# $WORKING_DIR_LOCATION_item = (Get-Item -Path $WORK_DIR)
	# $WORKING_DIR_LOCATION_fi = $WORKING_DIR_LOCATION_item.FileInfo
	# if ($WORKING_DIR_LOCATION_fi.BaseName -eq "airwindows") {
	# 	Write-Host "Trimming airwindows from arc location"
	# 	$WORKING_DIR_LOCATION = Split-Path -Path $WORK_DIR -Parent
	# } else {
	# }

	# if (-not (Test-Path -Path $WORK_DIR)) {
	# 	New-Item -Path $WORK -ItemType Directory -Verbose
	# }

	$PLUGINS_LOCATION = $PLUGS_DIR
} else {
	$PLUGINS_LOCATION = "F:\a\vstpluginsrepo\free.new\"
}

$PLUGINS_LOCATION = $PLUGINS_LOCATION.TrimEnd("\")

if (-not (Test-Path -Path $PLUGINS_LOCATION)) {
	Write-Error "Plugins $($PLUGINS_LOCATION) does not exist"
	exit 1
} else {
	Write-Host "Plugin location $($PLUGINS_LOCATION) exists"
}

$CURRENT_ARCHIVES_PART = $AW_ARC_DIR+"\_current-archives\"
$CURRENT_ARCHIVES_PATH = $WORKING_DIR_LOCATION+$CURRENT_ARCHIVES_PART

New-Item -Path $CURRENT_ARCHIVES_PATH -ItemType Directory -Verbose

$CURRENT_ARCHIVES_EXPLODED_PART = $CURRENT_ARCHIVES_PART+"_exploded\"

$LAST_ARCHIVES_PART = $AW_ARC_DIR+"\_last-archives\"
$LAST_ARC_PATH= $WORKING_DIR_LOCATION+$LAST_ARCHIVES_PART

New-Item -Path $LAST_ARC_PATH -ItemType Directory -Verbose

$DEST_ARCHIVES_PART = $AW_ARC_DIR+"\_destination-archives\"
$DEST_ARCHIVES_PATH = $WORKING_DIR_LOCATION+$DEST_ARCHIVES_PART

$EXPLODED_ARCHIVES_PART = "\_exploded"

New-Item -Path $DEST_ARCHIVES_PATH -ItemType Directory -Verbose

$DEST_EXPAND_PATH = $WORKING_DIR_LOCATION+$CURRENT_ARCHIVES_EXPLODED_PART

New-Item -Path $DEST_EXPAND_PATH -ItemType Directory -Verbose

$VST_64_PART = "\AirWindows"
$VST_86_PART = "\AirWindows-x86"

$VST_64_PATH = $PLUGINS_LOCATION+$VST_64_PART
$VST_86_PATH = $PLUGINS_LOCATION+$VST_86_PART

New-Item -Path $VST_64_PATH -ItemType Directory -Verbose
New-Item -Path $VST_86_PATH -ItemType Directory -Verbose

$FFS_CMD = "C:\Program Files\FreeFileSync\Bin\FreeFileSync_x64.exe"

if (-not (Test-Path -Path $FFS_CMD)) {
	Write-Error $FFS_CMD " does not exist"
	exit 1
} else {
	Write-Host "FreeFileSync $FFS_CMD exists"
}

$ARCHIVES = @(
	@{
		url = "https://www.airwindows.com/wp-content/uploads/WinVST64s.zip"
		current = $CURRENT_ARCHIVES_PATH+"WinVST64s.current.zip"
		last_arc = $LAST_ARC_PATH+"WinVST64s.last.zip"
		#dest_expand = $DEST_EXPAND_PATH+"WinVST64s"
		dest_expand = $DEST_EXPAND_PATH
		sync_dest = $VST_64_PATH
		sync_dest_arc = $DEST_ARCHIVES_PATH+"aw-64-free-new-dest-last.zip"
	},
	@{
		url = "https://www.airwindows.com/wp-content/uploads/RetroWinVST32s.zip"
		current = $CURRENT_ARCHIVES_PATH+"RetroWinVST32s.current.zip"
		last_arc = $LAST_ARC_PATH+"RetroWinVST32s.last.zip"
		#dest_expand = $DEST_EXPAND_PATH+"RetroWinVST32s"
		dest_expand = $DEST_EXPAND_PATH
		sync_dest = $VST_86_PATH
		sync_dest_arc = $DEST_ARCHIVES_PATH+"aw-86-free-new-dest-last.zip"
		sync_cmd = $FFS_CMD
		sync_args = "I:\proc\sync\4-AIR_WINDOWS_2.ffs_batch"
	},
	@{
		url = "https://www.airwindows.com/wp-content/uploads/LinuxVSTs.zip"
		current = $CURRENT_ARCHIVES_PATH+"LinuxVSTs.current.zip"
		last_arc = $LAST_ARC_PATH+"LinuxVSTs.last.zip"
		dest_expand = $DEST_EXPAND_PATH
	},
	@{
		url = "https://www.airwindows.com/wp-content/uploads/LinuxARMVSTs.zip"
		current = $CURRENT_ARCHIVES_PATH+"LinuxARMVSTs.current.zip"
		last_arc = $LAST_ARC_PATH+"LinuxARMVSTs.last.zip"
		dest_expand = $DEST_EXPAND_PATH
	}
)

Function ProcessArchives() {

	Write-Host "ProcessArchives"
	Write-Host

	# for each $ARCHIVES try to get them
	foreach ($url_job in $ARCHIVES) {

		Write-host "Pre sleep 1 seconds"
		Start-Sleep -Seconds 1

		Write-Host "Processing: "
		Write-Host 
		Write-Host "  url:           " $url_job.url
		Write-Host "  current:       " $url_job.current
		Write-Host "  last_arc:      " $url_job.last_arc
		Write-Host "  dest_expand: 	 " $url_job.dest_expand
		Write-Host "  sync_dest_arc: " $url_job.sync_dest_arc
		Write-Host "  sync_dest:     " $url_job.sync_dest
		Write-Host 

		$current_new_path = $url_job.current + "_new"
		$current_new_path_item = (Get-Item -Path $current_new_path)

		$should_download = $False

		if ($NO_AGE_CHECK) {$should_download = $True}
		if (-not $NO_AGE_CHECK -and (Test-Path -Path $current_new_path)) {
			# only do this if its actually old enough 
			$seven_days_6_hours = New-TimeSpan -days 7 -hours 6 -minutes 1
			#$five_days = New-TimeSpan -days 5
			if (((get-date) - $current_new_path_item.LastWriteTime) -gt $seven_days_6_hours) {
				Write-Host "Existing archive $($current_new_path) is old enough"
				Remove-Item -Path $current_new_path -Verbose
				$should_download = $True
			} else {
				Write-Host "Existing archive $($current_new_path) is too new "
				$should_download = $False
			}
		}
		else {
			Write-Host "Previous new item was not found"
			$should_download = $True
		}

		$status_code = $null
		$res = $null

		if ($should_download) {

			$download_archives_parameters = @{
				Uri = $url_job.url
				OutFile= $current_new_path
				#MaximumRetryCount= 3
				#RetryIntervalSec= 3
				Method= "Get"
				#Resume= "True"	
			}

			Write-Host "Download using parameters:- "
			$download_archives_parameters | Format-List | Out-Host
			try
			{
				$res = Invoke-WebRequest @download_archives_parameters
				# This will only execute if the Invoke-WebRequest is successful.
				$status_code = $Response.StatusCode
			} catch {
				$status_code = $_.Exception.Response.StatusCode.value__
			}

			if(-not 200 -eq $status_code) {
				Write-Error "Unable to download: $($url_job.url) to: " $current_new_path
				exit 1
			} else {
				Write-Host "Downloaded succeeded:-"
				(Get-ChildItem -Path $current_new_path)
			}

			# check if the zip is valid
			if (-not [SevenZipWrapper]::Test($current_new_path)) {
				Write-Error "Invalid Archive:- $($current_new_path)"
				exit 1
			} else {
				Write-Host "Archive is valid:- $($current_new_path)"
			}


		} else {
			Write-Host " Skip download ... "
		}

		if ($should_download -and -not $FORCE_ARCHIVE_CYCLE) {
			Write-Host

			(Get-ChildItem -Path $url_job.current)
			(Get-ChildItem -Path $url_job.last_arc)

			Write-Host

			# move the download archives
			# move current to last_arc
			Move-Item `
				-Path $url_job.current `
				-Destination $url_job.last_arc `
				-Force `
				-Verbose

			# copy new to current to avoid clobbering his webserver
			Copy-Item `
				-Path $current_new_path `
				-Destination $url_job.current `
				-Force `
				-Verbose

			Write-Host

			(Get-ChildItem -Path $current_new_path)
			(Get-ChildItem -Path $url_job.current)
			(Get-ChildItem -Path $url_job.last_arc)
			
			Write-Host

			# check args are correct
			# if ($url_job.dest_expand -or $url_job.sync_dest_arc) {

			# 	(Get-ChildItem -Path $url_job.dest_expand)
			# 	(Get-ChildItem -Path $url_job.sync_dest_arc)
				
			# 	if (-not $url_job.dest_expand) {
			# 		Write-Error ".dest_expand is missing"
			# 		Write-Error "Must have .dest and .sync_dest_arc"
			# 		exit 1
			# 	}
			# 	if (-not $url_job.sync_dest_arc) {
			# 		Write-Error ".sync_dest_arc is missing"
			# 		Write-Error "Must have .dest and .sync_dest_arc"
			# 		exit 1
			# 	}
			# }

			# before expanding the archive 7zip the current destination
			#if ($url_job.dest_expand -and $url_job.sync_dest_arc) {

			if ($url_job.dest_expand -or $url_job.sync_dest_arc) {
				Write-Host "Expanding download and compressing current..."

				if ($url_job.sync_dest_arc -and (Test-Path -Path $url_job.sync_dest_arc)) {
					Write-Host "$url_job.sync_dest_arc exists"

					# first move the last sync_dest_arc to _backup
					$dest_archive_backup = $url_job.sync_dest_arc + "_backup"
					Move-Item `
						-Path $url_job.sync_dest_arc `
						-Destination $dest_archive_backup `
						-Force
					
				} else {
					if ($url_job.sync_dest_arc) {
						Write-Host "$($url_job.sync_dest_arc) does not exist. Skipping backup."
					}
				}

				Write-Host

				# if there is a current in sync_dest archive it
				if ($url_job.sync_dest -and (Get-Item -Path $url_job.sync_dest).Count) {

					# too many results
					#(Get-ChildItem -Path $url_job.sync_dest)

					Write-Host "File(s) were found in: $($url_job.sync_dest) compressing dir"

					# [SevenZipWrapper]::Compress(
					# 	$url_job.dest_expand,
					# 	$url_job.sync_dest_arc
					# )
					Compress-Archive `
						-Path $url_job.sync_dest `
						-DestinationPath $url_job.sync_dest_arc `
						-CompressionLevel Optimal

					(Get-ChildItem -Path $url_job.sync_dest_arc)

				} else {
					Write-Host "$($url_job.dest_expand): is empty skipping 7zip compress"
				}

				$expand_archive_options = @{
					Path=$url_job.current
					DestinationPath=$url_job.dest_expand
				}
				$expand_archive_options.force = $True

				# if there is .dest expand the zip file to exploded
				Expand-Archive @expand_archive_options -Verbose

				(Get-ChildItem -Path $url_job.dest_expand)
			}

			$before_sync_file_count = (Get-ChildItem -Path $url_job.sync_dest).Count

			# if there is sync_cmd execute and wait for it
			if ($url_job.sync_cmd) {
				
				Write-Host "Performing synchronisation...."
				Write-Host "Before count: " $before_sync_file_count 

				Start-Process `
					-NoNewWindow `
					-FilePath $url_job.sync_cmd `
					-ArgumentList $url_job.sync_args `
					-Wait

				$after_sync_file_count = (Get-Item -Path $url_job.sync_dest).Count
				Write-Host "After count: " $after_sync_file_count

			}
			
		}

		Write-host "Post sleep 1 seconds"
		Start-Sleep -Seconds 1
	}


	#####################################
	# cleanup operations removing "__MACOSX" inside top level of zips

	# disabled since he might just do this at some point
	# if (-not (Test-Path -Path "$($WORKING_DIR_LOCATION)\Airwindows\_current\_exploded\__MACOSX").Exists) {
	# 	Write-Error "Exploded zips should contain __MACOSX dirs and don't"
	# 	exit 1
	# }

	$current_archives_macosx_dir_path = "$($CURRENT_ARCHIVES_PATH)$($EXPLODED_ARCHIVES_PART)\__MACOSX"

	Remove-Item -Path $current_archives_macosx_dir_path -Force -Verbose -Recurse

	Get-ChildItem -Path $current_archives_macosx_dir_path
}

#J:\dd\au\fx\AirWindows\_consolidated
$CONSOLIDATED_PART = $AW_ARC_DIR+"\_consolidated\"
$CONSOLIDATED_PATH = $WORKING_DIR_LOCATION+$CONSOLIDATED_PART

$CONSOLIDATED_JSON_FILE = "consolidated-releases.json"
$CONSOLIDATED_JSON_PATH = $CONSOLIDATED_PATH+$CONSOLIDATED_JSON_FILE

New-Item -Path $CONSOLIDATED_PATH -ItemType Directory -Verbose
Function ProcessConsolidatedByGithubApi() {

	if (-not $NO_AGE_CHECK) {
		if ((Test-Path -Path $CONSOLIDATED_JSON_PATH)) {
			Write-Host "$($CONSOLIDATED_JSON_PATH) exists... checking age..."

			# first check if it has been a week + 6 hours since the last time we downloaded the file
			$CONSOLIDATED_JSON_FILE_item = (Get-Item -Path $CONSOLIDATED_JSON_PATH)
			#$CONSOLIDATED_JSON_FILE_fi = $consolidated_json_item.FileInfo

			(Get-ChildItem -Path $CONSOLIDATED_JSON_PATH)

			$seven_days_6_hours = New-TimeSpan -days 7 -hours 6 -minutes 1
			# $seven_days = New-TimeSpan -days 7
			if (((get-date) - $CONSOLIDATED_JSON_FILE_item.LastWriteTime) -gt $seven_days_6_hours) {
				Write-Host "Current json file is older than 5 days"
			} else {
				Write-Error "Current json file still too new... exiting"
				return
			}
		} else {
			Write-Host "No previous $($CONSOLIDATED_JSON_PATH) was found...."
		}
	}

	if (-not $NO_CONSOLIDATED_DOWNLOAD) {
		# curl -L \
		# -H "Accept: application/vnd.github+json" \
		# -H "Authorization: Bearer <YOUR-TOKEN>" \
		# -H "X-GitHub-Api-Version: 2022-11-28" \
		# https://api.github.com/repos/OWNER/REPO/releases

		# /repos/{owner}/{repo}/releases/tags/{tag}
		$consolidated_api_uri = "https://api.github.com/repos/baconpaul/airwin2rack/releases/tags/DAWPlugin"

		# Replace with your actual access token
		$access_token = ConvertTo-SecureString -String "****************************************" -AsPlainText -Force
		
		$options = @{
			Uri = $consolidated_api_uri
			Method = "Get"
			Headers = @{
				"X-GitHub-Api-Version 2022-11-28" = ""
				"Accept" = "application/vnd.github+json"	
			}
			Authentication = "Bearer"
			Token = $access_token
		}

		$response = Invoke-WebRequest @options -SkipHeaderValidation
		
		# Process the response
		if ($response.StatusCode -eq 200) {
			# Success! Process the response data ($response.Content) as JSON
			Write-Host "Successfully retrieved releases."
		} else {
			# Error handling
			Write-Error "Failed to retrieve releases. Status code: $($response.StatusCode)"
			Write-Error "Error message: $($response.Content)"
		}

		$response.Content | Out-File -FilePath $CONSOLIDATED_JSON_PATH

		Write-Host "Content written to disk"

		# <a href="/baconpaul/airwin2rack/releases/download/DAWPlugin/AirwindowsConsolidated-2024-12-15-0fc84c0-Windows.zip" rel="nofollow" data-turbo="false" data-view-component="true" class="Truncate">
		# $matching_links = $consolidated_request_content | foreach { $_ -match "/baconpaul/airwin2rack/releases/download/DAWPlugin/" }

	} else {
		$response = @{ 
			Content = ""
		}
		$response.Content = Get-Content -Path $CONSOLIDATED_JSON_PATH -Verbose
	}

	$response.Content | Format-List | Out-Host -Paging

	$response_powershell_object = ConvertFrom-Json -InputObject $response.Content -AsHashtable
	#$_.assets[2].browser_download_url

	# 2 linux
	# 3 windows-64bit

	$release = ($response_powershell_object | Select-Object -Property {$_.assets[3].browser_download_url} ) -Join ""
	Write-Host "Release found: " $release
	Write-host "Release type: " $release.GetType()

	#@{$_.assets[2].browser_download_url=https://github.com/baconpaul/airwin2rack/releases/download/DAWPlugin/AirwindowsConsolidated-2024-12-15-0fc84c0-Windows-64bit-setup.exe}
	
	$consolidated_uri = $release.Replace("}","")
	$consolidated_uri = $consolidated_uri.Replace("`@`{`$`_.assets[3].browser_download_url=","")	

	$consolidated_file = Split-Path -Path $consolidated_uri -Leaf

	$consolidated_file_path = $CONSOLIDATED_PATH + $consolidated_file

	# check it doesnt already exist
	if ((Test-Path -Path $consolidated_file_path)) {
		Write-Host "$($consolidated_file_path) already exists.. exiting"
		return 
	} else {
		Write-Host "$($consolidated_file_path) does not exist"
	}

	Write-Host "Getting URI: $($consolidated_uri) TO: $($CONSOLIDATED_PATH) as FILE: $($consolidated_file)"

	$download_consolidated_options = @{
		Uri = $consolidated_uri
		OutFile = $consolidated_file_path
		#MaximumRetryCount = 3
		#RetryIntervalSec = 3
		Method = "Get"
		#Resume = "True"	
	}

	$status_code = $null
	$res = $null

	Write-Host "Download using parameters:- "
	$download_consolidated_options | Format-List | Out-Host

	try {
		$res = Invoke-WebRequest @download_consolidated_options
		# This will only execute if the Invoke-WebRequest is successful.
		$status_code = $Response.StatusCode
	} 
	catch {
		$status_code = $_.Exception.Response.StatusCode.value__
	}

	Write-Host "Download StatusCode: " $status_code
	Write-Host "StatusCodeDescription: " $res.StatusDescription

	(Get-ChildItem -Path $consolidated_file_path)

	#unlink $CONSOLIDATED_PATH + "current"
	#mklink $CONSOLIDATED_PATH + "current" $consolidated_file_path

	explorer $consolidated_file_path

}

$SRC_CODE = @(
	@{
		last_arc = "$($SRC_ARC_LOCATION)\airwindows-last.zip"
		src_path = "$($BUILD_SRC_LOCATION)\airwindows"
		update_cmd = "pull origin master"
	},
	@{
		last_arc = "$($SRC_ARC_LOCATION)\meter-last.zip"
		src_path = "$($BUILD_SRC_LOCATION)\meter"
		update_cmd = "pull origin"
		submodules_cmd = "submodule update --init"
	}
	# new console x plugins
)

Function ProcessSourceCode() {

	foreach ($src_job in $SRC_CODE) {

		Write-Host "Processing: "
		Write-Host 
		Write-Host "  last_arc:     	" $src_job.last_arc
		Write-Host "  src_path: 		" $src_job.src_path
		Write-Host "  update_cmd:		" $src_job.update_cmd
		Write-Host "  submodules_cmd:	" $src_job.submodules_cmd
		Write-Host 


		if (-not (Test-Path -Path $src_job.src_path)) {
			Write-Error " " $src_job.src_path " : DOES NOT EXIST"
			exit 1
		}

		# # make a 7zip of the current src code
		# [SevenZipWrapper]::Compress(
		# 	$url_job.src_path,
		# 	$url_job.sync_dest_arc
		# )

		# if (-not (Test-Path -Path $url_job.sync_dest_arc)) {
		# 	Write-Error " " $url_job.sync_dest_arc " : DOES NOT EXIST"
		# 	exit 1
		# }

		# # check the archive is valid
		# if (-not [SevenZipWrapper]::Test($url_job.sync_dest_arc)) {
		# 	Write-Error "Invalid Src Archive $($url_job.sync_dest_arc)"
		# 	exit 1
		# } else {
		# 	Write-Host "Src Archive is valid $($url_job.sync_dest_arc)"
		# }

		if($src_job.update_cmd) {
			Invoke-Expression "git -C $($src_job.src_path) $($src_job.update_cmd)"
		}

		if($src_job.submodules_cmd) {
			Invoke-Expression "git -C $($src_job.src_path) $($src_job.submodules_cmd)"
		}
	}
}

$ARCHIVES | Format-Table -Wrap | Out-Host

Write-Host "WORK: " $WORK
Write-Host "WORKING_DIR_LOCATION: " $WORKING_DIR_LOCATION

# the backup is another protection - so it doesn't delete them...

ProcessArchives
ProcessConsolidatedByGithubApi
ProcessSourceCode
