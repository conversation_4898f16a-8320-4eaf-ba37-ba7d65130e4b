ECHO Enable 4Dos BTM Mode
LOADBTM ON
REM *** MAIN ***
ECHO.
ECHO Protect local environment
SETLOCAL
ECHO.
IFF "%1" == "" THEN 
	GOSUB ERROR "Nothing to do! Specify /hs or /h for quick help and /hd fo detailed help"
ELSEIFF "%2" == "" THEN
	GOSUB ERROR "You have not specified a destination directory, just use /s to manually select"
ELSEIFF "%3" == "" THEN
	GOSUB ERROR "You have not specified a operation, just use /s to manually select"
ELSEIFF /I "%1" == "/h" THEN
	GOSUB HELP simple
ELSEIFF /I "%1" == "/hs" THEN  
	GOSUB HELP simple
ELSEIFF /I "%1" == "/hd" THEN
	GOSUB HELP detailed
ELSEIFF /I "%1" == "/s" THEN
	GOSUB ManualSelect
	GOSUB PerfOperation
ELSE	
	GOSUB PerfOperation
ENDIFF
GOTO END

:PerfOperation
CLS
IFF %Manual=="Y" THEN
	SWITCH %optChoice
		CASE v
			
		DEFAULT
	ENDSWITCH
ELSE	
		
ENDIFF
RETURN

:Opt 

RETURN

:ManualSelect
SET Manual=Y
CLS
ECHO You will be prompted for the src drive and directory, followed by the
ECHO destination drive and directory. No worries either we are only copying
ECHO files at this point.
ECHO.
ECHO PLEASE NOTE!!
ECHO.
ECHO 1. Mp3's must reside in the dir specified!!
ECHO 2. LFN's must be incased with quotation marks.
ECHO 3. No need to use a trailing \
ECHO.
INPUT src  ? %%srcdrive
ECHO.
INPUT dest ? %%destdrive
SELECT copy /r [%srcdrive\*.mp3] %destdrive
GOSUB simple
INPUT operation to perform ? %%optChoice
RETURN

:ERROR [errorStr]
ECHO.
ECHO Error:
ECHO.
ECHO %errorStr
ECHO.
RETURN

:HELP [level]
ECHO Synopsis:
ECHO.
ECHO ISO9660 MP3 filename formatter. Reduce, capitalize, lowercase and uppercase
ECHO operations for filenames. Specify source, destination dir and a argument to 
ECHO select the operation.
ECHO If you specify /s then the script will prompt you for necessary arguments.
ECHO.
ECHO This script EXPECTS well formed MP3 filenames, so you must use a hyphen to
ECHO deliminate categories you choose in your filenames.
ECHO.
ECHO Usage:
ECHO.
ECHO arcmp3-2-cd.btm [source-dir] [destination-dir] [operation]
ECHO.
IFF "%level" == "simple" THEN GOSUB simple
ELSEIFF "%level" == "detailed" THEN GOSUB detailed
ENDIFF
RETURN
	
	:simple
	ECHO Operation argument:- 
	ECHO remove (V)ouls, (C)apitalise 1st word only
	ECHO (CA) capitalise every word, (L)owercase, (U)ppercase
	RETURN
	
	:detailed
	ECHO The 3rd  argument is optional, if specified, it is case incensitive and will instruct the 
	ECHO script to perform the corresponding operation. based on the following possibilities:
	ECHO.
	ECHO (V)    - Remove vouls, from filenames to reduce length.
	ECHO.
	ECHO (C)    - Capitalize 1st letter of the 1st word for each filename.
	ECHO.
	ECHO (CA)   - Capitalize 1st letter and each subsequent word of the mp3 filename. This
	ECHO	   is delimeted by the presence of a hyphen.
	ECHO.
	ECHO (L)    - Convert filename to lowercase.
	ECHO.
	ECHO (U)    - Convert filename to uppercase.
	ECHO.
	RETURN

RETURN

:END
UNSET *
ENDLOCAL
ECHO.
ECHO ArcMp3-2-CD.2002_Beta_Version-0.1
ECHO.
QUIT