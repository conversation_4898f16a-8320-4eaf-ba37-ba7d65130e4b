@ECHO OFF
REM This script checks a file list... it checks if each thing exists and then prints out the failed ones

REM the input looks like this:- 
REM u:\a\red\bt.old\bmk\jovemcj drip drumkit\drums\rim\rim @jovemcj (6).wav

SET input_filelist=%1

FOR /F "tokens=*" %%i IN (%input_filelist%) DO (
    IF NOT EXIST "%%i" (
        ECHO Check: %%i Missing
    ) ELSE (
        REM ECHO Check: %%i Exists
    )
)
