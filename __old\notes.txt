* before a command uses a system one instead of 4nt version

small changes..

the backup scripts should take a parameter to auto close or not, 
so i can run from command line.
they should all use a universal alias's file.

the final consideration should be to the frequency of the backup regime. 
I've decided to backup the work everyday and keep a seperate archive 
just based on the day.

%1 %2 %3 %4 %5 %6 %7 %8 %9
